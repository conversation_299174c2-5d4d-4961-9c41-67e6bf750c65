const mongoose = require('mongoose');
const slugify = require('slugify');

// Define the variation schema for bales
const baleVariationSchema = new mongoose.Schema(
  {
    identifier: {
      type: String,
      required: [true, 'A variation must have an identifier']
    },
    quantity: {
      type: Number,
      required: [true, 'A variation must have a quantity'],
      min: [0, 'Quantity cannot be negative'],
      default: 0
    },
    quantityHistory: {
      type: [{
        quantity: {
          type: Number,
          required: true
        },
        changeAmount: {
          type: Number,
          required: true
        },
        changeType: {
          type: String,
          enum: ['initial', 'purchase', 'restock', 'adjustment', 'return'],
          required: true
        },
        reason: String,
        timestamp: {
          type: Date,
          default: Date.now
        },
        orderId: {
          type: mongoose.Schema.ObjectId,
          ref: 'Order'
        },
        updatedBy: {
          type: mongoose.Schema.ObjectId,
          ref: 'User'
        }
      }],
      default: []
    },
    // Track key inventory metrics separately for quick access
    inventoryMetrics: {
      lastRestocked: {
        date: Date,
        amount: Number,
        by: {
          type: mongoose.Schema.ObjectId,
          ref: 'User'
        }
      },
      lastPurchased: {
        date: Date,
        amount: Number,
        orderId: {
          type: mongoose.Schema.ObjectId,
          ref: 'Order'
        }
      },
      stockAlertSent: {
        lowStock: {
          type: Boolean,
          default: false
        },
        outOfStock: {
          type: Boolean,
          default: false
        },
        lastSent: Date
      }
    },
    lowStockThreshold: {
      type: Number,
      default: 3
    },
    price: {
      type: Number,
      required: [true, 'A variation must have a price']
    },
    salePrice: {
      type: Number,
      validate: {
        validator: function(val) {
          return val < this.price;
        },
        message: 'Sale price ({VALUE}) should be below regular price'
      }
    },
    saleStartDate: {
      type: Date
    },
    saleEndDate: {
      type: Date,
      validate: {
        validator: function(val) {
          return !this.saleStartDate || val > this.saleStartDate;
        },
        message: 'Sale end date must be after sale start date'
      }
    }
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Add a virtual property to check if the variation is on sale
baleVariationSchema.virtual('onSale').get(function() {
  if (!this.salePrice || !this.saleStartDate || !this.saleEndDate) {
    return false;
  }

  const now = new Date();
  return now >= this.saleStartDate && now <= this.saleEndDate;
});

// Add a virtual property to check if the variation has active promotions
baleVariationSchema.virtual('hasActivePromotion').get(function() {
  // This relies on the parent bale's hasActivePromotion virtual
  return this.parent().hasActivePromotion;
});

// Add a virtual property to get the promotional price
baleVariationSchema.virtual('promoPrice').get(function() {
  if (!this.hasActivePromotion) {
    return null;
  }

  const bestPromo = this.parent().bestPromotion;
  if (!bestPromo) {
    return null;
  }

  if (bestPromo.discountType === 'percentage') {
    return this.price * (1 - bestPromo.discountValue / 100);
  } else { // fixed discount
    return Math.max(0, this.price - bestPromo.discountValue);
  }
});

// Add a virtual property to get the current price (sale price, regular price, or global price)
baleVariationSchema.virtual('currentPrice').get(function() {
  // First check for promotional price (promotions take priority)
  if (this.hasActivePromotion) {
    return this.promoPrice;
  }
  
  // Then check for sale price
  if (this.onSale) {
    return this.salePrice;
  }
  
  // Default to regular price
  return this.price;
});

// Add a virtual property to get the discount percentage
baleVariationSchema.virtual('discountPercentage').get(function() {
  if (this.hasActivePromotion) {
    return Math.round(((this.price - this.promoPrice) / this.price) * 100);
  }
  
  if (this.onSale) {
    return Math.round(((this.price - this.salePrice) / this.price) * 100);
  }
  
  return 0;
});

// Add a virtual property to check if the variation has any discount (promo or sale)
baleVariationSchema.virtual('hasDiscount').get(function() {
  return this.hasActivePromotion || this.onSale;
});

// Add a virtual property to get the discount source
baleVariationSchema.virtual('discountSource').get(function() {
  if (this.hasActivePromotion) {
    return 'promotion';
  }
  
  if (this.onSale) {
    return 'sale';
  }
  
  return null;
});

// Add a virtual property to get the discount end date
baleVariationSchema.virtual('discountEndDate').get(function() {
  if (this.hasActivePromotion) {
    const bestPromo = this.parent().bestPromotion;
    return bestPromo ? bestPromo.endDate : null;
  }
  
  if (this.onSale) {
    return this.saleEndDate;
  }
  
  return null;
});

// Add a virtual property to check if stock is low
baleVariationSchema.virtual('isLowStock').get(function() {
  return this.quantity > 0 && this.quantity <= this.lowStockThreshold;
});

// Add a virtual property to check if out of stock
baleVariationSchema.virtual('isOutOfStock').get(function() {
  return this.quantity === 0;
});

// Add a virtual property to get the last quantity change
baleVariationSchema.virtual('lastQuantityChange').get(function() {
  if (!this.quantityHistory || this.quantityHistory.length === 0) {
    return null;
  }
  
  // Sort by timestamp descending and get the most recent entry
  const sortedHistory = [...this.quantityHistory].sort((a, b) => 
    new Date(b.timestamp) - new Date(a.timestamp)
  );
  
  return sortedHistory[0];
});

// Add a virtual property to get the last restock date and amount
baleVariationSchema.virtual('lastRestockInfo').get(function() {
  if (!this.quantityHistory || this.quantityHistory.length === 0) {
    return null;
  }
  
  // Filter for restock events and sort by timestamp descending
  const restockEvents = this.quantityHistory
    .filter(entry => entry.changeType === 'restock')
    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  
  if (restockEvents.length === 0) {
    return null;
  }
  
  return {
    date: restockEvents[0].timestamp,
    amount: restockEvents[0].changeAmount
  };
});

// Method to record a quantity change with detailed information
baleVariationSchema.methods.recordQuantityChange = function(options) {
  const {
    newQuantity,
    changeAmount,
    changeType = 'adjustment',
    reason = '',
    orderId = null,
    updatedBy = null
  } = options;
  
  // Mark that we're explicitly recording this change to avoid duplication in pre-save
  this._quantityChangeRecorded = true;
  
  // Store previous quantity for the pre-save middleware
  this._previousQuantity = this.quantity;
  
  // Update the quantity
  this.quantity = newQuantity;
  
  // Add to history (limited to 10 entries by pre-save hook)
  const historyEntry = {
    quantity: newQuantity,
    changeAmount: changeAmount,
    changeType: changeType,
    reason: reason,
    timestamp: new Date(),
    orderId: orderId,
    updatedBy: updatedBy
  };
  
  this.quantityHistory.push(historyEntry);
  
  // Update inventory metrics based on change type
  if (changeType === 'restock') {
    this.set('inventoryMetrics.lastRestocked', {
      date: new Date(),
      amount: changeAmount,
      by: updatedBy
    });
    this.lastRestocked = new Date();
  } else if (changeType === 'purchase') {
    this.set('inventoryMetrics.lastPurchased', {
      date: new Date(),
      amount: Math.abs(changeAmount),
      orderId: orderId
    });
  }
  
  return this;
};

// Replace the existing pre-save middleware with this comprehensive version
baleVariationSchema.pre('save', async function(next) {
  // Skip if no quantity modification or notifications are disabled
  if (!this.isModified('quantity') || this._skipNotifications) {
    return next();
  }

  // Record quantity change
  const currentQuantity = this.quantity;
  const previousQuantity = this.get('_originalQuantity') || 0;
  const changeAmount = currentQuantity - previousQuantity;

  // --- 1. Limit quantityHistory to 10 entries ---
  if (this.quantityHistory && this.quantityHistory.length > 10) {
    this.quantityHistory = this.quantityHistory.slice(-10);
  }

  // --- 2. Update lastRestocked if stock increased ---
  if (currentQuantity > previousQuantity) {
    this.lastRestocked = new Date();
  }

  // --- 3. Push to quantityHistory (fallback if not explicitly recorded) ---
  if (!this._quantityChangeRecorded) {
    const changeAmount = currentQuantity - previousQuantity;
    const changeType =
      changeAmount > 0 ? 'restock' :
      changeAmount < 0 ? 'adjustment' : 'initial';

    this.quantityHistory.push({
      quantity: currentQuantity,
      changeAmount,
      changeType,
      timestamp: new Date()
    });

    // Ensure only last 10 are kept
    if (this.quantityHistory.length > 10) {
      this.quantityHistory = this.quantityHistory.slice(-10);
    }
  }

  // --- 4. Handle stock notifications ---
  if (!this._skipNotifications) {
    try {
      const alertFlags = this.inventoryMetrics?.stockAlertSent || {};
      const bale = this.parent();

      if (currentQuantity > 0 && currentQuantity <= this.lowStockThreshold &&
          previousQuantity > this.lowStockThreshold && !alertFlags.lowStock) {

        this.set('inventoryMetrics.stockAlertSent.lowStock', true);
        this.set('inventoryMetrics.stockAlertSent.lastSent', new Date());

        if (bale?.creator) {
          await mongoose.model('Notification').create({
            recipient: bale.creator,
            type: 'low_stock',
            title: 'Low Stock Alert',
            message: `Your bale "${bale.name}" (${this.identifier}) is running low on stock. Only ${currentQuantity} units left.`,
            priority: 'medium',
            data: {
              bale: bale._id,
              url: `/dashboard/inventory/bales/${bale._id}`,
              additionalData: {
                variationId: this._id,
                currentStock: currentQuantity,
                threshold: this.lowStockThreshold
              }
            }
          });
        }
      }

      if (currentQuantity === 0 && previousQuantity > 0 && !alertFlags.outOfStock) {
        this.set('inventoryMetrics.stockAlertSent.outOfStock', true);
        this.set('inventoryMetrics.stockAlertSent.lastSent', new Date());

        if (bale?.creator) {
          await mongoose.model('Notification').create({
            recipient: bale.creator,
            type: 'out_of_stock',
            title: 'Out of Stock Alert',
            message: `Your bale "${bale.name}" (${this.identifier}) is now out of stock.`,
            priority: 'high',
            data: {
              bale: bale._id,
              url: `/dashboard/inventory/bales/${bale._id}`,
              additionalData: {
                variationId: this._id,
                previousStock: previousQuantity
              }
            }
          });
        }
      }

      if (currentQuantity > this.lowStockThreshold &&
         (alertFlags.lowStock || alertFlags.outOfStock)) {
        this.set('inventoryMetrics.stockAlertSent.lowStock', false);
        this.set('inventoryMetrics.stockAlertSent.outOfStock', false);
      }

    } catch (err) {
      console.error('Error sending stock notification:', err);
    }
  }

  next();
});


const baleSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'A bale must have a name'],
      trim: true,
      maxlength: [100, 'A bale name must have less or equal than 100 characters']
    },
    slug: String,
    description: {
      type: String,
      required: [true, 'A bale must have a description'],
      trim: true
    },
    highlights: [String],
    basePrice: {
      type: Number,
      required: [true, 'A bale must have a base price']
    },
    images: [String],
    category: {
      type: mongoose.Schema.ObjectId,
      ref: 'Category'
    },
    relatedCategories: [
      {
        type: mongoose.Schema.ObjectId,
        ref: 'Category'
      }
    ],
    tags: [String],
    creator: {
      type: mongoose.Schema.ObjectId,
      ref: 'User',
      required: [true, 'A bale must belong to a creator']
    },
    sold: {
      type: Number,
      default: 0
    },
    ratingsAverage: {
      type: Number,
      default: 0,
      min: [0, 'Rating must be above 0'],
      max: [5, 'Rating must be below 5.0'],
      set: val => Math.round(val * 10) / 10 // Round to 1 decimal place
    },
    ratingsQuantity: {
      type: Number,
      default: 0
    },
    status: {
      type: String,
      enum: ['draft', 'pending', 'active', 'inactive'],
      default: 'draft'
    },
    featured: {
      type: Boolean,
      default: false
    },
    // Bale-specific fields
    country: {
      type: String,
      required: [true, 'A bale must have a country of origin']
    },
    totalItems: {
      type: Number,
      required: [true, 'A bale must specify the total number of items'],
      min: [1, 'Total items must be at least 1']
    },
    weight: {
      type: Number,
      required: [true, 'A bale must have a weight']
    },
    dimensions: {
      length: Number,
      width: Number,
      height: Number
    },
    condition: {
      type: String,
      enum: ['New', 'Like New', 'Excellent', 'Good', 'Fair'],
      default: 'Good'
    },
    variations: [baleVariationSchema],
    // Optional list of products
    products: [
      {
        product: {
          type: mongoose.Schema.ObjectId,
          ref: 'Product'
        },
        quantity: {
          type: Number,
          min: [1, 'Quantity must be at least 1'],
          default: 1
        }
      }
    ],
    seo: {
      metaTitle: String,
      metaDescription: String,
      keywords: [String]
    }
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes
baleSchema.index({ basePrice: 1, ratingsAverage: -1 });
baleSchema.index({ slug: 1 });
baleSchema.index({ creator: 1 });
baleSchema.index({ category: 1 });
baleSchema.index({ country: 1 });
baleSchema.index({ condition: 1 });
baleSchema.index({ 'variations.identifier': 1 });
baleSchema.index({ name: 'text', description: 'text' }); // For text search
baleSchema.index({ createdAt: -1 }); // For sorting by date
baleSchema.index({ status: 1 }); // For filtering by status

// Virtual populate for reviews
baleSchema.virtual('reviews', {
  ref: 'Review',
  foreignField: 'bale',
  localField: '_id'
});

// Virtual property to get total stock across all variations
baleSchema.virtual('totalStock').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return 0;
  }

  return this.variations.reduce((total, variation) => total + variation.quantity, 0);
});

// Virtual property to get available identifier
baleSchema.virtual('availableIdentifiers').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return [];
  }

  const identifiers = new Set();
  this.variations.forEach(variation => {
    if (variation.quantity > 0) {
      identifiers.add(variation.identifier);
    }
  });

  return Array.from(identifiers);
});

// Virtual property to get the normal (non-discounted) minimum price across all variations
baleSchema.virtual('normalMinPrice').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return this.basePrice;
  }
  
  const prices = this.variations.map(variation => variation.price);
  return Math.min(...prices);
});

// Virtual property to get the normal (non-discounted) maximum price across all variations
baleSchema.virtual('normalMaxPrice').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return this.basePrice;
  }
  
  const prices = this.variations.map(variation => variation.price);
  return Math.max(...prices);
});

// Virtual property to get the discounted minimum price across all variations
baleSchema.virtual('discountedMinPrice').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return this.basePrice;
  }
  
  const prices = this.variations.map(variation => variation.currentPrice);
  return Math.min(...prices);
});

// Virtual property to get the discounted maximum price across all variations
baleSchema.virtual('discountedMaxPrice').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return this.basePrice;
  }
  
  const prices = this.variations.map(variation => variation.currentPrice);
  return Math.max(...prices);
});

// Virtual property to check if any variation has a discount
baleSchema.virtual('hasAnyDiscount').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return false;
  }
  
  return this.variations.some(variation => 
    variation.hasActivePromotion || variation.onSale
  );
});

// Virtual property to get the maximum discount percentage across all variations
baleSchema.virtual('maxDiscountPercentage').get(function() {
  if (!this.variations || this.variations.length === 0) {
    return 0;
  }
  
  let maxPercentage = 0;
  
  for (const variation of this.variations) {
    let discountPercentage = 0;
    
    if (variation.hasActivePromotion) {
      // Calculate promotion discount percentage
      discountPercentage = Math.round(((variation.price - variation.promoPrice) / variation.price) * 100);
    } else if (variation.onSale) {
      // Calculate sale discount percentage
      discountPercentage = Math.round(((variation.price - variation.salePrice) / variation.price) * 100);
    }
    
    if (discountPercentage > maxPercentage) {
      maxPercentage = discountPercentage;
    }
  }
  
  return maxPercentage;
});

// Virtual property to get formatted price range for display
baleSchema.virtual('formattedPriceRange').get(function() {
  if (this.hasAnyDiscount) {
    return {
      original: {
        min: this.normalMinPrice,
        max: this.normalMaxPrice,
        isSinglePrice: this.normalMinPrice === this.normalMaxPrice
      },
      discounted: {
        min: this.discountedMinPrice,
        max: this.discountedMaxPrice,
        isSinglePrice: this.discountedMinPrice === this.discountedMaxPrice
      },
      discountPercentage: this.maxDiscountPercentage
    };
  } else {
    return {
      original: {
        min: this.normalMinPrice,
        max: this.normalMaxPrice,
        isSinglePrice: this.normalMinPrice === this.normalMaxPrice
      },
      discounted: null,
      discountPercentage: 0
    };
  }
});

// Virtual property to get the earliest discount end date
baleSchema.virtual('discountEndDate').get(function() {
  if (!this.hasAnyDiscount) {
    return null;
  }
  
  // Get variations with active discounts (either promotion or sale)
  const discountedVariations = this.variations.filter(v => v.hasDiscount);
  
  // Extract end dates from each variation's active discount
  const endDates = discountedVariations.map(v => {
    // For each variation, get the end date of the discount that's actually being used
    // (the one that determines the currentPrice)
    if (v.hasActivePromotion && v.discountSource === 'promotion') {
      // If promotion is the active discount source, use the promotion end date
      const bestPromo = this.promoPrice;
      return bestPromo ? bestPromo.endDate : null;
    } else if (v.onSale && v.discountSource === 'sale') {
      // If sale is the active discount source, use the sale end date
      return v.saleEndDate;
    }
    return null;
  });
  
  // Filter out null dates
  const validDates = endDates.filter(date => date !== null);
  
  if (validDates.length === 0) {
    return null;
  }
  
  // Return the earliest end date (the first discount that will expire)
  return new Date(Math.min(...validDates.map(d => d.getTime())));
});

// Virtual property to get the discount type (promotion or sale)
baleSchema.virtual('discountType').get(function() {
  if (!this.hasAnyDiscount) {
    return null;
  }
  
  // Prioritize promotion over sale if both exist
  if (this.variations.some(v => v.hasActivePromotion)) {
    return 'promotion';
  }
  
  return 'sale';
});

// Virtual property to check if the product has any active promotions
baleSchema.virtual('hasActivePromotion').get(function() {
  if (!this.promotions || this.promotions.length === 0) {
    return false;
  }

  const now = new Date();
  return this.promotions.some(promo => 
    promo.isActive && 
    now >= promo.startDate && 
    now <= promo.endDate && 
    promo.promoStock > 0
  );
});

// Virtual property to get the best active promotion
baleSchema.virtual('bestPromotion').get(function() {
  if (!this.hasActivePromotion) {
    return null;
  }

  const now = new Date();
  const activePromotions = this.promotions.filter(promo => 
    promo.isActive && 
    now >= promo.startDate && 
    now <= promo.endDate && 
    promo.promoStock > 0
  );

  if (activePromotions.length === 0) {
    return null;
  }

  // Return the promotion with the highest discount value
  return activePromotions.reduce((best, current) => {
    // For percentage discounts, higher percentage is better
    // For fixed discounts, higher amount is better
    if (best.discountType === current.discountType) {
      return current.discountValue > best.discountValue ? current : best;
    }
    
    // If different types, calculate effective discount on a sample price (e.g., 100)
    const samplePrice = 100;
    const bestDiscount = best.discountType === 'percentage' 
      ? samplePrice * (best.discountValue / 100) 
      : best.discountValue;
    
    const currentDiscount = current.discountType === 'percentage' 
      ? samplePrice * (current.discountValue / 100) 
      : current.discountValue;
    
    return currentDiscount > bestDiscount ? current : best;
  }, activePromotions[0]);
});

// Import image cleanup utilities
const { createImageCleanupMiddleware, executeImageCleanup } = require('../utils/cloudinaryCleanup');

// DOCUMENT MIDDLEWARE: runs before .save() and .create()
baleSchema.pre('save', function(next) {
  this.slug = slugify(this.name, { lower: true });
  next();
});

// Image cleanup middleware - runs before save to track image changes
baleSchema.pre('save', createImageCleanupMiddleware(['images']));

// Post-save middleware to execute image cleanup
baleSchema.post('save', executeImageCleanup);

// Pre-remove middleware to cleanup all images when bale is deleted
baleSchema.pre('remove', async function(next) {
  try {
    const { cleanupEntityImages } = require('../utils/cloudinaryCleanup');

    // Execute cleanup for bale images
    const result = await cleanupEntityImages(this, ['images']);
    console.log(`Cleaned up ${result.deleted} images for deleted bale ${this._id}`);

    next();
  } catch (error) {
    console.error('Error cleaning up bale images:', error);
    // Don't fail the deletion due to cleanup issues
    next();
  }
});

// Pre-deleteOne middleware for findByIdAndDelete operations
baleSchema.pre('deleteOne', { document: true, query: false }, async function(next) {
  try {
    const { cleanupEntityImages } = require('../utils/cloudinaryCleanup');

    // Execute cleanup for bale images
    const result = await cleanupEntityImages(this, ['images']);
    console.log(`Cleaned up ${result.deleted} images for deleted bale ${this._id}`);

    next();
  } catch (error) {
    console.error('Error cleaning up bale images:', error);
    // Don't fail the deletion due to cleanup issues
    next();
  }
});

// QUERY MIDDLEWARE
baleSchema.pre(/^find/, function(next) {
  this.populate({
    path: 'creator',
    select: 'name photo shopInfo businessInfo.businessName verificationStatus metrics.qualityScore metrics.averageRating metrics.shippingSpeed'
  }).populate({
    path: 'category',
    select: 'name'
  }).populate({
    path: 'relatedCategories',
    select: 'name'
  });

  next();
});

const Bale = mongoose.model('Bale', baleSchema);

module.exports = Bale;