const mongoose = require('mongoose');
const validator = require('validator');
const BaseUser = require('./baseUser.model');
const Review = require('./review.model');
const Product = require('./product.model');
const Bale = require('./bale.model');
const Order = require('./order.model');
const Buyer = require('./buyer.model');

const creatorSchema = new mongoose.Schema({
  businessInfo: {
    businessName: {
      type: String,

    },
    businessType: {
      type: String,
      enum: ['individual', 'sole_proprietorship', 'partnership', 'corporation', 'llc']
      // Remove required constraint
    },
    ownerName: String, // Remove required constraint
    ownerID: String, // Remove required constraint
    taxId: String,
    businessAddress: {
      addressLine1: String, // Remove required constraint
      addressLine2: String,
      city: String, // Remove required constraint
      state: String, // Remove required constraint
      country: {
        type: String,
        default: 'Ghana'
      },
      digitalGps: String
    },
    phoneNumber: String, // Remove required constraint
    isVerified: {
      type: Boolean,
      default: false
    },
    verificationDocuments: [String]
  },
  // Keep metrics as is since they have defaults
  metrics: {
    totalSales: {
      type: Number,
      default: 0
    },
    totalRevenue: {
      type: Number,
      default: 0
    },
    averageRating: {
      type: Number,
      default: 0,
      min: [0, 'Rating cannot be less than 0'],
      max: [5, 'Rating cannot be more than 5']
    },
    qualityScore: {
      type: Number,
      default: 0,
      min: [0, 'Quality score cannot be less than 0'],
      max: [100, 'Quality score cannot be more than 100']
    },
    shippingSpeed: {
      type: Number,
      default: 0, // Average shipping time in days
      min: [0, 'Shipping speed cannot be negative'],
      max: [100, 'Shipping speed cannot be more than 100']
    },
    totalProducts: {
      type: Number,
      default: 0
    },
    totalBales: {
      type: Number,
      default: 0
    },
    followers: {
      type: Number,
      default: 0
    }
  },
  paymentInfo: {
    paymentOption: {
      type: String,
      enum: ['bank', 'mobile_money']
      // Remove required constraint
    },
    bankDetails: {
      beneficiaryName: String,
      accountNumber: String,
      bankName: String,
      bankBranch: String,
      swiftCode: String
    },
    mobileMoneyDetails: {
      serviceProvider: String,
      registeredName: String,
      registeredNumber: String
    }
  },
  payoutPreferences: {
    frequency: {
      type: String,
      enum: ['weekly', 'biweekly', 'monthly', 'on_demand'],
      default: 'monthly'
    },
    minimumAmount: {
      type: Number,
      default: 5,
      min: [5, 'Minimum payout amount cannot be less than 50']
    }
  },
  shopInfo: {
    name: {
      type: String,
      trim: true
      // Remove required constraint
    },
    logo: String,
    banner: String,
    description: {
      type: String,
      trim: true
    },
    contact: {
      name: String, // Remove required constraint
      email: {
        type: String,
        lowercase: true,
        validate: [validator.isEmail, 'Please provide a valid email']
        // Remove required constraint
      },
      phone: String // Remove required constraint
    },
    customerCare: {
      name: String,
      phone: String,
      email: {
        type: String,
        lowercase: true,
        validate: [validator.isEmail, 'Please provide a valid email']
      },
      addressLine1: String,
      addressLine2: String,
      city: String,
      region: String,
      country: {
        type: String,
        default: 'Ghana'
      }
    }
  },
  shippingInfo: {
    shippingAddress: {
      addressLine1: String, // Remove required constraint
      addressLine2: String,
      city: String, // Remove required constraint
      state: String, // Remove required constraint
      country: {
        type: String,
        default: 'Ghana'
      },
      digitalGps: String, // New field for digital GPS
      gpsAddress: String, // Keep for backward compatibility
      zone: String,
      phone: String // Add phone field
    },
    returnAddress: {
      useSameAddress: {
        type: Boolean,
        default: false
      },
      addressLine1: String,
      addressLine2: String,
      city: String,
      state: String,
      digitalGps: String, // New field for digital GPS
      gpsAddress: String, // Keep for backward compatibility
      zone: String,
      phone: String, // Add phone field
      country: {
        type: String,
        default: 'Ghana'
      },
      gpsAddress: String
    }
  },
  verificationStatus: {
    type: String,
    enum: ['unverified', 'pending', 'verified', 'rejected'],
    default: 'unverified'
  },
  verificationDetails: {
    submittedAt: Date,
    reviewedAt: Date,
    rejectionReason: String,
    documents: [String]
  },
  commissionRate: {
    type: Number,
    default: 5,
    min: [0, 'Commission rate cannot be less than 0'],
    max: [100, 'Commission rate cannot be more than 100']
  },
  socialMedia: {
    instagram: String,
    twitter: String,
    facebook: String,
    tiktok: String,
    pinterest: String,
    youtube: String
  },

  onboardingStatus: {
    type: String,
    enum: ['pending', 'completed'],
    default: 'pending'
  },
  onboardingProgress: {
    businessInfo: {
      type: Boolean,
      default: false
    },
    paymentInfo: {
      type: Boolean,
      default: false
    },
    shopInfo: {
      type: Boolean,
      default: false
    },
    shippingInfo: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Add method to check onboarding completion
creatorSchema.methods.isOnboardingComplete = function() {
  return Object.values(this.onboardingProgress).every(status => status === true);
};

// Add method to update onboarding progress
creatorSchema.methods.updateOnboardingProgress = function() {
  // Check business info completion
  this.onboardingProgress.businessInfo = !!(
    this.businessInfo.businessName &&
    this.businessInfo.businessType &&
    this.businessInfo.ownerName &&
    this.businessInfo.ownerID &&
    this.businessInfo.phoneNumber
  );



  // Check payment info completion
  this.onboardingProgress.paymentInfo = !!(
    this.paymentInfo.paymentOption &&
    (
      (this.paymentInfo.paymentOption === 'bank' &&
       this.paymentInfo.bankDetails.accountNumber &&
       this.paymentInfo.bankDetails.bankName &&
       this.paymentInfo.bankDetails.beneficiaryName) ||
      (this.paymentInfo.paymentOption === 'mobile_money' &&
       this.paymentInfo.mobileMoneyDetails.registeredNumber &&
       this.paymentInfo.mobileMoneyDetails.serviceProvider &&
       this.paymentInfo.mobileMoneyDetails.registeredName)
    )
  );
  

  // Check shop info completion
  // First, check if shopInfo exists and has required properties
  const shopInfoExists = !!this.shopInfo;
  const hasName = shopInfoExists && !!this.shopInfo.name;
  const hasContact = shopInfoExists && !!this.shopInfo.contact;
  const hasContactName = hasContact && !!this.shopInfo.contact.name;
  const hasContactEmail = hasContact && !!this.shopInfo.contact.email;
  const hasContactPhone = hasContact && !!this.shopInfo.contact.phone;


  // Set shop info completion status
  const allShopInfoFieldsValid = shopInfoExists && hasName && hasContact && hasContactName &&
                                hasContactEmail && hasContactPhone;

  this.onboardingProgress.shopInfo = allShopInfoFieldsValid;


  // Check shipping info completion
  const shippingInfoExists = !!this.shippingInfo && !!this.shippingInfo.shippingAddress;

  this.onboardingProgress.shippingInfo = shippingInfoExists && !!(
    this.shippingInfo.shippingAddress.addressLine1 &&
    this.shippingInfo.shippingAddress.city &&
    this.shippingInfo.shippingAddress.state &&
    this.shippingInfo.shippingAddress.country &&
    this.shippingInfo.shippingAddress.phone &&
    this.shippingInfo.shippingAddress.gpsAddress &&
    this.shippingInfo.shippingAddress.digitalGps
  );


  // Update overall onboarding status - simplified to just pending or completed
  if (this.isOnboardingComplete()) {
    this.onboardingStatus = 'completed';
  } else {
    this.onboardingStatus = 'pending';
  }
};

// Add middleware to update onboarding progress before save
creatorSchema.pre('save', function(next) {
  // Store the current onboarding progress values
  const currentProgress = {
    businessInfo: this.onboardingProgress.businessInfo,
    paymentInfo: this.onboardingProgress.paymentInfo,
    shopInfo: this.onboardingProgress.shopInfo,
    shippingInfo: this.onboardingProgress.shippingInfo
  };

  // Update the progress
  this.updateOnboardingProgress();

  // Restore any manually set values that were true
  if (currentProgress.businessInfo) this.onboardingProgress.businessInfo = true;
  if (currentProgress.paymentInfo) this.onboardingProgress.paymentInfo = true;
  if (currentProgress.shopInfo) this.onboardingProgress.shopInfo = true;
  if (currentProgress.shippingInfo) this.onboardingProgress.shippingInfo = true;

  // Check if all steps are complete after restoring values
  if (this.onboardingProgress.businessInfo &&
      this.onboardingProgress.paymentInfo &&
      this.onboardingProgress.shopInfo &&
      this.onboardingProgress.shippingInfo) {
    this.onboardingStatus = 'completed';
  }

  next();
});


// Add strategic indexes for common query patterns
creatorSchema.index({ verificationStatus: 1, 'metrics.totalRevenue': -1 }); // Compound index for verified creators by revenue
creatorSchema.index({ 'businessInfo.businessName': 1 }, { unique: true, sparse: true }); // Unique index for business name
creatorSchema.index({ 'shopInfo.name': 'text' }); // Text index for search
creatorSchema.index({ createdAt: -1 }); // For date-based queries
creatorSchema.index({ 'metrics.averageRating': -1, 'metrics.totalSales': -1 }); // For sorting by popularity

// Virtual populate for creator's products
creatorSchema.virtual('products', {
  ref: 'Product',
  foreignField: 'creator',
  localField: '_id',
  options: {
    limit: 10,
    sort: { createdAt: -1 },
    select: 'name formattedPriceRange images status variations' // Select only needed fields
  }
});

// Virtual populate for creator's bales
creatorSchema.virtual('bales', {
  ref: 'Bale',
  foreignField: 'creator',
  localField: '_id',
  options: {
    limit: 10,
    sort: { createdAt: -1 },
    select: 'name formattedPriceRange images status variations' // Select only needed fields
  }
});

// Virtual populate for creator's reviews
creatorSchema.virtual('reviews', {
  ref: 'Review',
  foreignField: 'creator',
  localField: '_id',
  options: {
    limit: 5,
    sort: { createdAt: -1 }
  }
});

// Virtual populate for creator's payouts
creatorSchema.virtual('payouts', {
  ref: 'Payout',
  foreignField: 'creator',
  localField: '_id',
  options: {
    limit: 10,
    sort: { createdAt: -1 }
  }
});

// Virtual populate for creator's orders
creatorSchema.virtual('orders', {
  ref: 'Order',
  foreignField: 'items.creator',
  localField: '_id',
  options: {
    limit: 10,
    sort: { createdAt: -1 }
  }
});

// Set default role to 'creator'
creatorSchema.pre('save', function(next) {
  if (!this.role) {
    this.role = 'creator';
  }
  next();
});

// Add middleware to ensure data consistency
creatorSchema.pre('save', function(next) {
  // If using same address for returns, copy shipping address
  if (this.shippingInfo.returnAddress.useSameAddress) {
    this.shippingInfo.returnAddress = {
      ...this.shippingInfo.shippingAddress,
      useSameAddress: true
    };
  }
  next();
});

// Add selective updates
creatorSchema.pre('save', async function(next) {
  // Only update metrics if relevant fields changed
  const metricsFields = ['businessInfo', 'shopInfo', 'verificationStatus'];
  const shouldUpdateMetrics = metricsFields.some(field => this.isModified(field));

  if (!shouldUpdateMetrics) return next();

  try {
    // Use Promise.all for parallel execution
    const [productCount, baleCount] = await Promise.all([
      Product.countDocuments({ creator: this._id }),
      Bale.countDocuments({ creator: this._id })
    ]);

    // Batch aggregation queries
    const [orderStats, shippingStats, reviewStats, followerCount] = await Promise.all([
      // Basic order stats
      Order.aggregate([
        {
          $match: {
            'items.creator': this._id,
            createdAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) } // Last 30 days only
          }
        },
        {
          $group: {
            _id: null,
            totalSales: { $sum: '$items.quantity' },
            totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
          }
        }
      ]),

      // Shipping and delivery stats
      Order.aggregate([
        {
          $match: {
            'items.creator': this._id,
            status: { $in: ['shipped', 'delivered'] },
            createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // Last 90 days for more data
          }
        },
        {
          $project: {
            shippingDays: {
              $cond: [
                { $eq: ['$status', 'shipped'] },
                { $divide: [{ $subtract: [new Date(), '$createdAt'] }, 1000 * 60 * 60 * 24] }, // Current time - order time for in-progress
                { $divide: [{ $subtract: ['$deliveredAt', '$createdAt'] }, 1000 * 60 * 60 * 24] } // Delivered time - order time for completed
              ]
            },
            isDelivered: { $eq: ['$status', 'delivered'] },
            isOnTime: {
              $cond: [
                { $eq: ['$status', 'delivered'] },
                // Assuming 7 days is the standard delivery time
                { $lte: [{ $divide: [{ $subtract: ['$deliveredAt', '$createdAt'] }, 1000 * 60 * 60 * 24] }, 7] },
                false
              ]
            }
          }
        },
        {
          $group: {
            _id: null,
            avgShippingDays: { $avg: '$shippingDays' },
            totalDelivered: { $sum: { $cond: ['$isDelivered', 1, 0] } },
            totalOnTime: { $sum: { $cond: ['$isOnTime', 1, 0] } },
            totalOrders: { $sum: 1 }
          }
        },
        {
          $project: {
            avgShippingDays: 1,
            onTimeRate: {
              $cond: [
                { $eq: ['$totalDelivered', 0] },
                0,
                { $multiply: [{ $divide: ['$totalOnTime', '$totalDelivered'] }, 100] }
              ]
            },
            totalOrders: 1
          }
        }
      ]),

      Review.aggregate([
        {
          $match: {
            creator: this._id,
            createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // Last 90 days only
          }
        },
        { $group: { _id: null, averageRating: { $avg: '$rating' } } }
      ]),

      Buyer.countDocuments({ followedCreators: this._id })
    ]);

    // Calculate quality score based on ratings and on-time delivery
    const avgRating = reviewStats[0]?.averageRating || 0;
    const onTimeRate = shippingStats[0]?.onTimeRate || 0;

    // Quality score formula: 50% from ratings (0-5 scale) and 50% from on-time delivery rate (0-100 scale)
    // Convert rating to 0-100 scale and take weighted average
    const qualityScore = Math.round((avgRating * 20 * 0.5) + (onTimeRate * 0.5));

    // Update metrics
    this.metrics = {
      totalProducts: productCount,
      totalBales: baleCount,
      followers: followerCount,
      totalSales: orderStats[0]?.totalSales || this.metrics.totalSales,
      totalRevenue: orderStats[0]?.totalRevenue || this.metrics.totalRevenue,
      averageRating: avgRating || this.metrics.averageRating,
      qualityScore: qualityScore,
      shippingSpeed: shippingStats[0]?.avgShippingDays || this.metrics.shippingSpeed
    };

    next();
  } catch (error) {
    next(error);
  }
});

// Add method for paginated products
creatorSchema.methods.getProducts = async function(page = 1, limit = 10) {
  return Product
    .find({ creator: this._id })
    .select('name formattedPriceRange images status')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .lean();
};

// Method to calculate quality score
creatorSchema.methods.calculateQualityScore = async function() {
  try {
    // Get review stats
    const reviewStats = await Review.aggregate([
      {
        $match: {
          creator: this._id,
          createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // Last 90 days only
        }
      },
      { $group: { _id: null, averageRating: { $avg: '$rating' } } }
    ]);

    // Get shipping stats
    const shippingStats = await Order.aggregate([
      {
        $match: {
          'items.creator': this._id,
          status: { $in: ['shipped', 'delivered'] },
          createdAt: { $gte: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000) } // Last 90 days for more data
        }
      },
      {
        $project: {
          shippingDays: {
            $cond: [
              { $eq: ['$status', 'shipped'] },
              { $divide: [{ $subtract: [new Date(), '$createdAt'] }, 1000 * 60 * 60 * 24] },
              { $divide: [{ $subtract: ['$deliveredAt', '$createdAt'] }, 1000 * 60 * 60 * 24] }
            ]
          },
          isDelivered: { $eq: ['$status', 'delivered'] },
          isOnTime: {
            $cond: [
              { $eq: ['$status', 'delivered'] },
              { $lte: [{ $divide: [{ $subtract: ['$deliveredAt', '$createdAt'] }, 1000 * 60 * 60 * 24] }, 7] },
              false
            ]
          }
        }
      },
      {
        $group: {
          _id: null,
          avgShippingDays: { $avg: '$shippingDays' },
          totalDelivered: { $sum: { $cond: ['$isDelivered', 1, 0] } },
          totalOnTime: { $sum: { $cond: ['$isOnTime', 1, 0] } },
          totalOrders: { $sum: 1 }
        }
      },
      {
        $project: {
          avgShippingDays: 1,
          onTimeRate: {
            $cond: [
              { $eq: ['$totalDelivered', 0] },
              0,
              { $multiply: [{ $divide: ['$totalOnTime', '$totalDelivered'] }, 100] }
            ]
          }
        }
      }
    ]);

    // Calculate metrics
    const avgRating = reviewStats[0]?.averageRating || 0;
    const onTimeRate = shippingStats[0]?.onTimeRate || 0;
    const avgShippingDays = shippingStats[0]?.avgShippingDays || 0;

    // Quality score formula: 50% from ratings (0-5 scale) and 50% from on-time delivery rate (0-100 scale)
    const qualityScore = Math.round((avgRating * 20 * 0.5) + (onTimeRate * 0.5));

    return {
      qualityScore,
      averageRating: avgRating,
      shippingSpeed: avgShippingDays
    };
  } catch (error) {
    console.error('Error calculating quality score:', error);
    return {
      qualityScore: this.metrics.qualityScore || 0,
      averageRating: this.metrics.averageRating || 0,
      shippingSpeed: this.metrics.shippingSpeed || 0
    };
  }
};

// Add methods for different view types
creatorSchema.statics.findByIdLean = function(id) {
  return this.findById(id)
    .select('-verificationDocuments -paymentInfo -notificationPreferences')
    .lean();
};

creatorSchema.statics.findForDashboard = function(id) {
  return this.findById(id)
    .select('businessInfo.businessName metrics shopInfo.name verificationStatus')
    .lean();
};

creatorSchema.statics.findForPublicProfile = function(id) {
  return this.findById(id)
    .select('businessInfo.businessName shopInfo metrics')
    .lean();
};

// Import image cleanup utilities
const { createImageCleanupMiddleware, executeImageCleanup } = require('../utils/cloudinaryCleanup');

// Image cleanup middleware - runs before save to track image changes
creatorSchema.pre('save', createImageCleanupMiddleware(['photo', 'shopInfo.logo', 'shopInfo.banner', 'businessInfo.verificationDocuments']));

// Post-save middleware to execute image cleanup
creatorSchema.post('save', executeImageCleanup);

// Pre-remove middleware to cleanup all images when creator is deleted
creatorSchema.pre('remove', async function(next) {
  try {
    const { cleanupEntityImages } = require('../utils/cloudinaryCleanup');

    // Collect all image fields
    const imageFields = ['photo'];

    // Add shop info images if they exist
    if (this.shopInfo?.logo) imageFields.push('shopInfo.logo');
    if (this.shopInfo?.banner) imageFields.push('shopInfo.banner');

    // Add verification documents if they exist
    if (this.businessInfo?.verificationDocuments?.length > 0) {
      imageFields.push('businessInfo.verificationDocuments');
    }

    // Execute cleanup
    const result = await cleanupEntityImages(this, imageFields);
    console.log(`Cleaned up ${result.deleted} images for deleted creator ${this._id}`);

    next();
  } catch (error) {
    console.error('Error cleaning up creator images:', error);
    // Don't fail the deletion due to cleanup issues
    next();
  }
});

const Creator = BaseUser.discriminator('Creator', creatorSchema);

module.exports = Creator;










