const express = require('express');
const baleController = require('../../controllers/creators/bale.controller');
const uploadMiddleware = require('../../middleware/cloudinaryUpload.middleware');


const router = express.Router();


// Bale routes
router.get('/', baleController.getMyBales);
router.post('/',
  uploadMiddleware.uploadBaleImages,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.processNestedFormData,
  uploadMiddleware.handleUploadError,
  baleController.createBale
);
router.get('/stats', baleController.getBaleStats);
router.get('/:id', baleController.getMyBale);
router.patch('/:id',
  uploadMiddleware.uploadBaleImages,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.processNestedFormData,
  uploadMiddleware.handleUploadError,
  baleController.updateMyBale
);
router.delete('/:id', baleController.deleteMyBale);

module.exports = router;
