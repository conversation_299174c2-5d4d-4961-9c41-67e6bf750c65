const express = require('express');
const profileController = require('../../controllers/creators/profile.controller');
const uploadMiddleware = require('../../middleware/cloudinaryUpload.middleware');

const router = express.Router();

// Profile routes
router.get('/', profileController.getProfile);
router.patch('/',
  uploadMiddleware.uploadProfilePhoto,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.handleUploadError,
  profileController.updateProfile
);

// Profile photo route
router.patch('/photo',
  uploadMiddleware.uploadProfilePhoto,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.handleUploadError,
  profileController.updateProfilePhoto
);
// Business Info routes
router.get('/business-info', profileController.getBusinessInfo);
router.patch('/business-info',
  uploadMiddleware.uploadVerificationDocuments,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.processNestedFormData,
  uploadMiddleware.handleUploadError,
  profileController.updateBusinessInfo
);

// Payment Info routes
router.get('/payment-info', profileController.getPaymentInfo);
router.patch('/payment-info', profileController.updatePaymentInfo);

// Shop Info routes
router.get('/shop-info', profileController.getShopInfo);
router.patch('/shop-info',
  uploadMiddleware.uploadShopMedia,
  uploadMiddleware.processCloudinaryFiles,
  uploadMiddleware.processNestedFormData,
  uploadMiddleware.handleUploadError,
  profileController.updateShopInfo
);

// Shipping Info routes
router.get('/shipping-info', profileController.getShippingInfo);
router.patch('/shipping-info',
  uploadMiddleware.processNestedFormData,
  profileController.updateShippingInfo
);
router.get('/dashboard', profileController.getDashboardStats);

module.exports = router;
