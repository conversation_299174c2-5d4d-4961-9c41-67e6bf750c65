const mongoose = require('mongoose');
const Product = require('../../models/product.model');
const Category = require('../../models/category.model');
const Review = require('../../models/review.model');
const Order = require('../../models/order.model');
const Promotion = require('../../models/promotion.model');
const { Creator } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get all products for the logged-in creator
 * @route GET /api/v1/creators/products
 * @access Private (Creator only)
 */
exports.getMyProducts = catchAsync(async (req, res, next) => {
  // Build query
  const queryObj = { ...req.query, creator: req.user.id };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);

  let query = Product.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { brand: searchRegex }
      ]
    });
  }

  // Filter by status if specified
  if (req.query.status) {
    query = query.find({ status: req.query.status });
  }

  // Filter by stock level if specified
  if (req.query.stockLevel) {
    if (req.query.stockLevel === 'low') {
      // Products with total stock > 0 and <= 10
      query = query.aggregate([
        { $match: { creator: new mongoose.Types.ObjectId(req.user.id) } },
        { $addFields: { totalStock: { $sum: '$variations.quantity' } } },
        { $match: { totalStock: { $gt: 0, $lte: 10 } } }
      ]);
    } else if (req.query.stockLevel === 'out') {
      // Products with total stock = 0
      query = query.aggregate([
        { $match: { creator: new mongoose.Types.ObjectId(req.user.id) } },
        { $addFields: { totalStock: { $sum: '$variations.quantity' } } },
        { $match: { totalStock: 0 } }
      ]);
    }
  }

  // Handle aggregation vs regular query for counting
  let total;
  if (req.query.stockLevel) {
    // For stock level filtering, we need to use aggregation for counting too
    const countPipeline = [
      { $match: { creator: new mongoose.Types.ObjectId(req.user.id) } }
    ];

    // Add search filter to aggregation if specified
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      countPipeline.push({
        $match: {
          $or: [
            { name: searchRegex },
            { description: searchRegex },
            { brand: searchRegex }
          ]
        }
      });
    }

    // Add status filter to aggregation if specified
    if (req.query.status) {
      countPipeline.push({ $match: { status: req.query.status } });
    }

    // Add stock level filter
    countPipeline.push({ $addFields: { totalStock: { $sum: '$variations.quantity' } } });

    if (req.query.stockLevel === 'low') {
      countPipeline.push({ $match: { totalStock: { $gt: 0, $lte: 10 } } });
    } else if (req.query.stockLevel === 'out') {
      countPipeline.push({ $match: { totalStock: 0 } });
    }

    countPipeline.push({ $count: 'total' });

    const countResult = await Product.aggregate(countPipeline);
    total = countResult[0]?.total || 0;
  } else {
    // Regular counting for non-stock filters
    const countQuery = { ...JSON.parse(queryStr) };
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      countQuery.$or = [
        { name: searchRegex },
        { description: searchRegex },
        { brand: searchRegex }
      ];
    }
    if (req.query.status) {
      countQuery.status = req.query.status;
    }
    countQuery.creator = req.user.id;

    total = await Product.countDocuments(countQuery);
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  let products;

  if (req.query.stockLevel) {
    // Handle aggregation pipeline for stock filtering
    const pipeline = [
      { $match: { creator: new mongoose.Types.ObjectId(req.user.id) } }
    ];

    // Add search filter to aggregation if specified
    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      pipeline.push({
        $match: {
          $or: [
            { name: searchRegex },
            { description: searchRegex },
            { brand: searchRegex }
          ]
        }
      });
    }

    // Add status filter to aggregation if specified
    if (req.query.status) {
      pipeline.push({ $match: { status: req.query.status } });
    }

    // Add stock level filter
    pipeline.push({ $addFields: { totalStock: { $sum: '$variations.quantity' } } });

    if (req.query.stockLevel === 'low') {
      pipeline.push({ $match: { totalStock: { $gt: 0, $lte: 10 } } });
    } else if (req.query.stockLevel === 'out') {
      pipeline.push({ $match: { totalStock: 0 } });
    }

    // Add sorting
    if (req.query.sort) {
      const sortField = req.query.sort.replace('-', '');
      const sortOrder = req.query.sort.startsWith('-') ? -1 : 1;
      pipeline.push({ $sort: { [sortField]: sortOrder } });
    } else {
      pipeline.push({ $sort: { createdAt: -1 } });
    }

    // Add pagination
    pipeline.push({ $skip: skip });
    pipeline.push({ $limit: limit });

    // Add population
    pipeline.push({
      $lookup: {
        from: 'categories',
        localField: 'category',
        foreignField: '_id',
        as: 'category',
        pipeline: [{ $project: { name: 1, description: 1 } }]
      }
    });
    pipeline.push({
      $lookup: {
        from: 'categories',
        localField: 'relatedCategories',
        foreignField: '_id',
        as: 'relatedCategories',
        pipeline: [{ $project: { name: 1, description: 1 } }]
      }
    });

    // Unwind category (since it's a single reference)
    pipeline.push({
      $unwind: {
        path: '$category',
        preserveNullAndEmptyArrays: true
      }
    });

    products = await Product.aggregate(pipeline);
  } else {
    // Handle regular query for non-stock filters
    // Sorting
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('-createdAt');
    }

    // Field limiting
    if (req.query.fields) {
      const fields = req.query.fields.split(',').join(' ');
      query = query.select(fields);
    } else {
      query = query.select('-__v');
    }

    query = query.skip(skip).limit(limit);

    // Execute query with population
    products = await query
      .populate({
        path: 'category',
        select: 'name description'
      })
      .populate({
        path: 'relatedCategories',
        select: 'name description'
      });
  }

  res.status(200).json({
    status: 'success',
    results: products.length,
    total,
    page,
    limit,
    data: {
      products
    }
  });
});

/**
 * Get product by ID for the logged-in creator
 * @route GET /api/v1/creators/products/:id
 * @access Private (Creator only)
 */
exports.getMyProduct = catchAsync(async (req, res, next) => {
  const product = await Product.findOne({
    _id: req.params.id,
    creator: req.user.id
  })
  .populate({
    path: 'category',
    select: 'name description'
  })
  .populate({
    path: 'relatedCategories',
    select: 'name description'
  });

  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      product
    }
  });
});


/**
 * Create a new product
 * @route POST /api/v1/creators/products
 * @access Private (Creator only)
 */
exports.createProduct = catchAsync(async (req, res, next) => {
  // Add creator to req.body
  req.body.creator = req.user.id;

  // Fast validation using a validation schema approach
  const validationErrors = validateProductData(req.body);
  if (validationErrors.length > 0) {
    return next(new AppError(validationErrors[0], 400));
  }

  // Set initial status to pending for admin approval
  req.body.status = 'pending';

  // Ensure ObjectId conversions (batch process)
  if (req.body.category && typeof req.body.category === 'string') {
    req.body.category = new mongoose.Types.ObjectId(req.body.category);
  }

  if (req.body.relatedCategories && Array.isArray(req.body.relatedCategories)) {
    req.body.relatedCategories = req.body.relatedCategories.map(id => {
      if (typeof id === 'string') {
        return new mongoose.Types.ObjectId(id);
      }
      return id;
    });
  }

  // Create the product using fast method without auto-population to improve performance
  const newProduct = await Product.createFast(req.body);

  // Update creator's product count asynchronously (don't wait for it)
  Creator.findByIdAndUpdate(req.user.id, {
    $inc: { 'metrics.pendingProducts': 1 }
  }).exec().catch(err => {
    console.error('Error updating creator metrics:', err);
  });

  res.status(201).json({
    status: 'success',
    data: {
      product: newProduct
    }
  });
});

// Helper function for fast validation
function validateProductData(data) {
  const errors = [];

  // Required fields validation
  const requiredFields = ['name', 'brand', 'description', 'basePrice', 'category', 'gender'];
  for (const field of requiredFields) {
    if (!data[field]) {
      errors.push(`Please provide ${field}`);
      break; // Return first error for speed
    }
  }

  if (errors.length > 0) return errors;

  // Images validation
  if (!data.images || !Array.isArray(data.images) || data.images.length === 0) {
    errors.push('Please provide at least one product image');
    return errors;
  }

  // Price validation
  if (data.basePrice < 0) {
    errors.push('Base price cannot be negative');
    return errors;
  }

  // Gender validation
  const validGenders = ['Men', 'Women', 'Unisex', 'Boys', 'Girls'];
  if (!validGenders.includes(data.gender)) {
    errors.push(`Gender must be one of: ${validGenders.join(', ')}`);
    return errors;
  }

  // Category validation
  if (!mongoose.Types.ObjectId.isValid(data.category)) {
    errors.push('Please provide a valid category ID');
    return errors;
  }

  // Related categories validation
  if (data.relatedCategories) {
    if (!Array.isArray(data.relatedCategories)) {
      errors.push('Related categories must be an array');
      return errors;
    }

    for (const categoryId of data.relatedCategories) {
      if (!mongoose.Types.ObjectId.isValid(categoryId)) {
        errors.push('Please provide valid related category IDs');
        return errors;
      }
    }
  }

  // Variations validation
  if (!data.variations || !Array.isArray(data.variations) || data.variations.length === 0) {
    errors.push('Please provide at least one product variation');
    return errors;
  }

  // Fast variation validation
  const variationErrors = validateVariations(data.variations);
  if (variationErrors.length > 0) {
    errors.push(variationErrors[0]);
    return errors;
  }

  // Fast optional field validation
  if (data.specifications) {
    const specs = data.specifications;
    const arrayFields = ['occasion', 'season'];
    for (const field of arrayFields) {
      if (specs[field] && !Array.isArray(specs[field])) {
        errors.push(`${field} must be an array`);
        return errors;
      }
    }
  }

  if (data.highlights && !Array.isArray(data.highlights)) {
    errors.push('Highlights must be an array');
    return errors;
  }

  if (data.tags && !Array.isArray(data.tags)) {
    errors.push('Tags must be an array');
    return errors;
  }

  return errors;
}

// Helper function for fast variation validation
function validateVariations(variations) {
  const errors = [];

  for (const variation of variations) {
    // Check required fields
    const variationRequiredFields = ['color', 'size', 'quantity', 'price'];
    for (const field of variationRequiredFields) {
      if (!variation[field] && variation[field] !== 0) {
        errors.push(`Please provide ${field} for each variation`);
        return errors; // Return first error for speed
      }
    }

    // Validate quantity and price
    if (variation.quantity < 0) {
      errors.push('Quantity cannot be negative');
      return errors;
    }

    if (variation.price < 0) {
      errors.push('Price cannot be negative');
      return errors;
    }

    // Validate sale price if provided
    if (variation.salePrice) {
      if (variation.salePrice >= variation.price) {
        errors.push('Sale price must be lower than regular price');
        return errors;
      }

      if (!variation.saleStartDate || !variation.saleEndDate) {
        errors.push('Sale start date and end date are required when sale price is provided');
        return errors;
      }

      const startDate = new Date(variation.saleStartDate);
      const endDate = new Date(variation.saleEndDate);

      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        errors.push('Invalid sale dates');
        return errors;
      }

      if (startDate >= endDate) {
        errors.push('Sale end date must be after sale start date');
        return errors;
      }
    }

    // Validate variation images if provided
    if (variation.images && !Array.isArray(variation.images)) {
      errors.push('Variation images must be an array');
      return errors;
    }
  }

  return errors;
}

/**
 * Update product by ID for the logged-in creator
 * @route PATCH /api/v1/creators/products/:id
 * @access Private (Creator only)
 */
exports.updateMyProduct = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const product = await Product.findOne({ _id: id, creator: req.user.id });
  if (!product) return next(new AppError('No product found with that ID', 404));

  // Special handling for active products - only allow variations update
  if (product.status === 'active') {
    // Only allow variations to be updated for active products
    if (!req.body.variations) {
      return next(new AppError('For active products, only variations can be updated', 400));
    }

    // Check if any other fields are being updated
    const allowedFieldsForActive = ['variations'];
    const attemptedFields = Object.keys(req.body);
    const disallowedFields = attemptedFields.filter(field => !allowedFieldsForActive.includes(field));

    if (disallowedFields.length > 0) {
      return next(new AppError(`For active products, only variations can be updated. Cannot update: ${disallowedFields.join(', ')}`, 400));
    }

    // Handle variations update for active products
    if (!Array.isArray(req.body.variations) || req.body.variations.length === 0) {
      return next(new AppError('Please provide at least one product variation', 400));
    }

    const existingVariationIds = product.variations.map(v => v._id.toString());
    const updatedVariations = [];

    for (const variation of req.body.variations) {
      if (!variation._id) {
        return next(new AppError('Cannot add new variations to active products', 400));
      }

      if (!existingVariationIds.includes(variation._id.toString())) {
        return next(new AppError(`Variation with ID ${variation._id} not found in this product`, 404));
      }

      const existingVariation = product.variations.id(variation._id);

      // For active products, only allow updating specific variation fields
      const allowedVariationFields = ['price', 'salePrice', 'saleStartDate', 'saleEndDate', 'quantity'];
      const providedFields = Object.keys(variation).filter(key => key !== '_id');
      const disallowedVariationFields = providedFields.filter(field => !allowedVariationFields.includes(field));

      if (disallowedVariationFields.length > 0) {
        return next(new AppError(`For active products, only ${allowedVariationFields.join(', ')} can be updated in variations. Cannot update: ${disallowedVariationFields.join(', ')}`, 400));
      }

      // Validate variation fields
      if (variation.quantity !== undefined && variation.quantity < 0) {
        return next(new AppError('Quantity cannot be negative', 400));
      }

      if (variation.price !== undefined && variation.price < 0) {
        return next(new AppError('Price cannot be negative', 400));
      }

      if (variation.salePrice !== undefined) {
        if (variation.salePrice >= (variation.price || existingVariation.price)) {
          return next(new AppError('Sale price must be lower than regular price', 400));
        }

        // Ensure sale dates are provided when sale price is updated
        const saleStartDate = variation.saleStartDate || existingVariation.saleStartDate;
        const saleEndDate = variation.saleEndDate || existingVariation.saleEndDate;

        if (!saleStartDate || !saleEndDate) {
          return next(new AppError('Sale start date and end date are required when sale price is provided', 400));
        }

        const startDate = new Date(saleStartDate);
        const endDate = new Date(saleEndDate);

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return next(new AppError('Invalid sale dates', 400));
        }

        if (startDate >= endDate) {
          return next(new AppError('Sale end date must be after sale start date', 400));
        }
      }

      // Update allowed fields
      allowedVariationFields.forEach(field => {
        if (variation[field] !== undefined) {
          existingVariation[field] = variation[field];
        }
      });

      updatedVariations.push(existingVariation);
    }

    // Keep existing variations that weren't updated
    const variationsToKeep = product.variations.filter(
      v => !req.body.variations.some(updateV => updateV._id && updateV._id.toString() === v._id.toString())
    );

    // Update the product with only the variations
    const updatedProduct = await Product.findByIdAndUpdate(
      id,
      { variations: [...variationsToKeep, ...updatedVariations] },
      { new: true, runValidators: true }
    );

    // Populate category and relatedCategories
    if (updatedProduct.category) {
      const category = await Category.findById(updatedProduct.category);
      if (category) {
        updatedProduct.category = {
          _id: category._id,
          name: category.name,
          description: category.description,
          pathArray: category.name.split(' > '),
          id: category._id
        };
      }
    }

    if (updatedProduct.relatedCategories?.length > 0) {
      const populated = [];

      for (const id of updatedProduct.relatedCategories) {
        const cat = await Category.findById(id);
        if (cat) {
          populated.push({
            _id: cat._id,
            name: cat.name,
            description: cat.description,
            pathArray: cat.name.split(' > '),
            id: cat._id
          });
        }
      }

      updatedProduct.relatedCategories = populated;
    }

    return res.status(200).json({
      status: 'success',
      data: {
        product: updatedProduct
      }
    });
  }

  // For non-active products, continue with the existing logic
  // Check if product is in a state that can be updated
  if (!['draft', 'pending', 'rejected', 'inactive'].includes(product.status)) {
    return next(new AppError(`Cannot update product in ${product.status} status`, 400));
  }

  if (req.body.basePrice !== undefined && req.body.basePrice < 0) {
    return next(new AppError('Base price cannot be negative', 400));
  }

  if (req.body.gender) {
    const validGenders = ['Men', 'Women', 'Unisex', 'Boys', 'Girls'];
    if (!validGenders.includes(req.body.gender)) {
      return next(new AppError(`Gender must be one of: ${validGenders.join(', ')}`, 400));
    }
  }

  if (req.body.category && !mongoose.Types.ObjectId.isValid(req.body.category)) {
    return next(new AppError('Please provide a valid category ID', 400));
  }

  if (req.body.relatedCategories) {
    if (!Array.isArray(req.body.relatedCategories)) {
      return next(new AppError('Related categories must be an array', 400));
    }

    for (const categoryId of req.body.relatedCategories) {
      if (!mongoose.Types.ObjectId.isValid(categoryId)) {
        return next(new AppError('Please provide valid related category IDs', 400));
      }
    }
  }

  const updatedVariations = [];
  if (req.body.variations) {
    if (!Array.isArray(req.body.variations) || req.body.variations.length === 0) {
      return next(new AppError('Please provide at least one product variation', 400));
    }

    const existingVariationIds = product.variations.map(v => v._id.toString());

    for (const variation of req.body.variations) {
      if (variation._id) {
        if (!existingVariationIds.includes(variation._id.toString())) {
          return next(new AppError(`Variation with ID ${variation._id} not found in this product`, 404));
        }

        const existingVariation = product.variations.id(variation._id);

        Object.keys(variation).forEach(key => {
          if (key !== '_id') {
            if (key === 'quantity' && variation[key] < 0) {
              return next(new AppError('Quantity cannot be negative', 400));
            }
            if (key === 'price' && variation[key] < 0) {
              return next(new AppError('Price cannot be negative', 400));
            }
            existingVariation[key] = variation[key];
          }
        });

        updatedVariations.push(existingVariation);
      } else {
        const requiredFields = ['color', 'size', 'quantity', 'price'];
        for (const field of requiredFields) {
          if (!variation[field] && variation[field] !== 0) {
            return next(new AppError(`Please provide ${field} for each new variation`, 400));
          }
        }

        if (variation.quantity < 0) {
          return next(new AppError('Quantity cannot be negative', 400));
        }

        if (variation.price < 0) {
          return next(new AppError('Price cannot be negative', 400));
        }

        if (variation.salePrice && variation.salePrice >= variation.price) {
          return next(new AppError('Sale price must be lower than regular price', 400));
        }

        if (variation.salePrice) {
          if (!variation.saleStartDate || !variation.saleEndDate) {
            return next(new AppError('Sale start and end dates are required when sale price is provided', 400));
          }

          const start = new Date(variation.saleStartDate);
          const end = new Date(variation.saleEndDate);

          if (isNaN(start) || isNaN(end)) {
            return next(new AppError('Invalid sale dates', 400));
          }

          if (start >= end) {
            return next(new AppError('Sale end date must be after sale start date', 400));
          }
        }

        if (variation.images && !Array.isArray(variation.images)) {
          return next(new AppError('Variation images must be an array', 400));
        }

        updatedVariations.push(variation);
      }
    }

    const variationsToKeep = product.variations.filter(
      v => !req.body.variations.some(updateV => updateV._id && updateV._id.toString() === v._id.toString())
    );

    req.body.variations = [...variationsToKeep, ...updatedVariations];
  }

  if (req.body.specifications) {
    const specs = req.body.specifications;

    

    const arrayFields = ['occasion', 'season'];
    arrayFields.forEach(field => {
      if (specs[field] && !Array.isArray(specs[field])) {
        return next(new AppError(`${field} must be an array`, 400));
      }
    });
  }

  if (req.body.highlights && !Array.isArray(req.body.highlights)) {
    return next(new AppError('Highlights must be an array', 400));
  }

  if (req.body.tags && !Array.isArray(req.body.tags)) {
    return next(new AppError('Tags must be an array', 400));
  }

  const filteredBody = {};
  const allowedFields = ['name', 'brand', 'description', 'basePrice', 'category', 'relatedCategories', 'tags', 'gender', 'highlights', 'specifications', 'variations', 'images', 'seo'];

  for (const key in req.body) {
    if (allowedFields.includes(key)) {
      filteredBody[key] = req.body[key];
    }
  }

  // Set status to pending for admin approval if product was inactive
  // (active products are handled separately at the beginning of the function)
  if (product.status === 'inactive') {
    filteredBody.status = 'pending';

    // Update creator's product counts
    await Creator.findByIdAndUpdate(req.user.id, {
      $inc: { 'metrics.pendingProducts': 1 }
    });
  }

  if (filteredBody.category && typeof filteredBody.category === 'string') {
    filteredBody.category = new mongoose.Types.ObjectId(filteredBody.category);
  }

  if (Array.isArray(filteredBody.relatedCategories)) {
    filteredBody.relatedCategories = filteredBody.relatedCategories.map(id =>
      typeof id === 'string' ? new mongoose.Types.ObjectId(id) : id
    );
  }

  const updatedProduct = await Product.findByIdAndUpdate(id, filteredBody, {
    new: true,
    runValidators: true
  });

  // Populate category
  if (updatedProduct.category) {
    const category = await Category.findById(updatedProduct.category);
    if (category) {
      updatedProduct.category = {
        _id: category._id,
        name: category.name,
        description: category.description,
        pathArray: category.name.split(' > '),
        id: category._id
      };
    }
  }

  // Populate related categories
  if (updatedProduct.relatedCategories?.length > 0) {
    const populated = [];

    for (const id of updatedProduct.relatedCategories) {
      const cat = await Category.findById(id);
      if (cat) {
        populated.push({
          _id: cat._id,
          name: cat.name,
          description: cat.description,
          pathArray: cat.name.split(' > '),
          id: cat._id
        });
      }
    }

    updatedProduct.relatedCategories = populated;
  }

  res.status(200).json({
    status: 'success',
    data: {
      product: updatedProduct
    }
  });
});


/**
 * Delete product by ID for the logged-in creator
 * @route DELETE /api/v1/creators/products/:id
 * @access Private (Creator only)
 */
exports.deleteMyProduct = catchAsync(async (req, res, next) => {
  // Find product
  const product = await Product.findOne({
    _id: req.params.id,
    creator: req.user.id
  });

  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Check if product is in a state that can be deleted
  if (!['draft', 'pending', 'rejected', 'inactive'].includes(product.status)) {
    return next(new AppError(`Cannot delete product in ${product.status} status`, 400));
  }

  // Update creator's product counts
  if (product.status === 'pending') {
    await Creator.findByIdAndUpdate(req.user.id, {
      $inc: { 'metrics.pendingProducts': -1 }
    });
  } else if (product.status === 'inactive') {
    await Creator.findByIdAndUpdate(req.user.id, {
      $inc: { 'metrics.inactiveProducts': -1 }
    });
  }

  // Delete the product
  await Product.findByIdAndDelete(req.params.id);

  res.status(204).json({
    status: 'success',
    data: null
  });
});

/**
 * Get product counts by status and stock levels for filtering
 * @route GET /api/v1/creators/products/counts
 * @access Private (Creator only)
 */
exports.getProductCounts = catchAsync(async (req, res, next) => {
  const creatorId = req.user.id;

  // Single aggregation to get all counts efficiently
  const counts = await Product.aggregate([
    { $match: { creator: new mongoose.Types.ObjectId(creatorId) } },
    {
      $facet: {
        // Status-based counts
        statusCounts: [
          {
            $group: {
              _id: null,
              all: { $sum: 1 },
              approved: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
              pending: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] } },
              rejected: { $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] } }
            }
          }
        ],
        // Stock-based counts
        stockCounts: [
          { $unwind: '$variations' },
          {
            $group: {
              _id: '$_id',
              totalStock: { $sum: '$variations.quantity' },
              minStock: { $min: '$variations.quantity' }
            }
          },
          {
            $group: {
              _id: null,
              lowStock: {
                $sum: {
                  $cond: [
                    {
                      $and: [
                        { $gt: ['$totalStock', 0] },
                        { $lte: ['$totalStock', 10] }
                      ]
                    },
                    1,
                    0
                  ]
                }
              },
              outOfStock: {
                $sum: {
                  $cond: [{ $eq: ['$totalStock', 0] }, 1, 0]
                }
              }
            }
          }
        ]
      }
    }
  ]);

  // Extract results
  const statusData = counts[0]?.statusCounts[0] || {
    all: 0,
    approved: 0,
    pending: 0,
    rejected: 0
  };

  const stockData = counts[0]?.stockCounts[0] || {
    lowStock: 0,
    outOfStock: 0
  };

  res.status(200).json({
    status: 'success',
    data: {
      all: statusData.all,
      approved: statusData.approved,
      pending: statusData.pending,
      rejected: statusData.rejected,
      lowStock: stockData.lowStock,
      outOfStock: stockData.outOfStock
    }
  });
});

/**
 * OPTIMIZED: Get product statistics for the logged-in creator
 * @route GET /api/v1/creators/products/stats
 * @access Private (Creator only)
 */
exports.getProductStats = async (req, res) => {
  try {
    const { period } = req.query;
    const creatorId = req.user._id;

    // Pre-compute date filter for efficiency
    const dateFilter = getDateFilter(period);
    const matchQuery = { creator: creatorId, ...dateFilter };

    // Single aggregation to get all product stats at once
    const productStatsPromise = Product.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          avgPrice: { $avg: '$basePrice' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // Single aggregation to get all product counts at once
    const productCountsPromise = Product.aggregate([
      { $match: matchQuery },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          draft: { $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] } },
          pending: { $sum: { $cond: [{ $eq: ['$status', 'pending'] }, 1, 0] } },
          active: { $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] } },
          inactive: { $sum: { $cond: [{ $eq: ['$status', 'inactive'] }, 1, 0] } },
          rejected: { $sum: { $cond: [{ $eq: ['$status', 'rejected'] }, 1, 0] } }
        }
      }
    ]);

    // Optimized sales and top products aggregation (combined)
    const salesAndTopProductsPromise = Order.aggregate([
      {
        $match: {
          ...dateFilter,
          status: { $in: ['completed', 'delivered'] },
          'items.creator': creatorId
        }
      },
      { $unwind: '$items' },
      { $match: { 'items.creator': creatorId } },
      {
        $facet: {
          // Sales statistics
          salesStats: [
            {
              $group: {
                _id: null,
                totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
                totalUnitsSold: { $sum: '$items.quantity' },
                totalOrders: { $sum: 1 }
              }
            }
          ],
          // Top products
          topProducts: [
            {
              $group: {
                _id: '$items.product',
                totalSold: { $sum: '$items.quantity' },
                totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
              }
            },
            { $sort: { totalRevenue: -1 } },
            { $limit: 5 },
            {
              $lookup: {
                from: 'products',
                localField: '_id',
                foreignField: '_id',
                as: 'product'
              }
            },
            { $unwind: '$product' },
            {
              $project: {
                _id: 1,
                name: '$product.name',
                totalSold: 1,
                totalRevenue: 1
              }
            }
          ]
        }
      }
    ]);

    // Execute all queries in parallel for maximum performance
    const [productStats, productCounts, salesAndTopProducts] = await Promise.all([
      productStatsPromise,
      productCountsPromise,
      salesAndTopProductsPromise
    ]);

    // Extract results
    const counts = productCounts[0] || {
      total: 0, draft: 0, pending: 0, active: 0, inactive: 0, rejected: 0
    };

    const salesData = salesAndTopProducts[0];
    const salesStats = salesData.salesStats[0] || {
      totalRevenue: 0, totalUnitsSold: 0, totalOrders: 0
    };
    const topProducts = salesData.topProducts || [];

    return res.status(200).json({
      status: 'success',
      data: {
        period: period || 'all_time',
        stats: productStats,
        summary: counts,
        sales: salesStats,
        topProducts
      }
    });

  } catch (error) {
    return res.status(500).json({
      status: 'error',
      message: 'Failed to fetch product statistics'
    });
  }
};

// Helper function for date filtering
function getDateFilter(period) {
  const now = new Date();

  switch (period) {
    case 'today':
      return {
        createdAt: {
          $gte: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
          $lt: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)
        }
      };
    case 'week':
      const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
      return { createdAt: { $gte: weekStart } };
    case 'month':
      return {
        createdAt: {
          $gte: new Date(now.getFullYear(), now.getMonth(), 1)
        }
      };
    case 'year':
      return {
        createdAt: {
          $gte: new Date(now.getFullYear(), 0, 1)
        }
      };
    default:
      return {}; // All time - no date filter
  }
}

/**
 * Update basic product information
 * @route PATCH /api/v1/creators/products/:id/basic-info
 * @access Private (Creator only)
 */
exports.updateBasicInfo = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // Check if request body exists
  if (!req.body || Object.keys(req.body).length === 0) {
    return next(new AppError('Please provide data to update', 400));
  }

  const product = await Product.findOne({ _id: id, creator: req.user.id });
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Check if request body exists and has data
  if (!req.body || Object.keys(req.body).length === 0) {
    return next(new AppError('Please provide data to update', 400));
  }

  // Validate category if provided
  if (req.body.category) {
    const category = await Category.findById(req.body.category);
    if (!category) {
      return next(new AppError('Invalid category ID', 400));
    }
  }

  // Validate related categories if provided
  if (req.body.relatedCategories) {
    if (!Array.isArray(req.body.relatedCategories)) {
      return next(new AppError('Related categories must be an array', 400));
    }

    if (req.body.relatedCategories.length > 0) {
      const categories = await Category.find({ _id: { $in: req.body.relatedCategories } });
      if (categories.length !== req.body.relatedCategories.length) {
        return next(new AppError('Please provide valid related category IDs', 400));
      }
    }
  }

  // Check if request body exists and has data
  if (!req.body || Object.keys(req.body).length === 0) {
    return next(new AppError('Please provide data to update', 400));
  }

  // Build update object with only provided fields
  const updateData = {};

  // Basic info fields - only update if provided
  if (req.body.name !== undefined) updateData.name = req.body.name;
  if (req.body.brand !== undefined) updateData.brand = req.body.brand;
  if (req.body.description !== undefined) updateData.description = req.body.description;
  if (req.body.highlights !== undefined) updateData.highlights = req.body.highlights;
  if (req.body.gender !== undefined) updateData.gender = req.body.gender;
  if (req.body.basePrice !== undefined) updateData.basePrice = req.body.basePrice;
  if (req.body.tags !== undefined) updateData.tags = req.body.tags;

  // Handle category - convert to ObjectId if provided
  if (req.body.category !== undefined) {
    updateData.category = typeof req.body.category === 'string'
      ? new mongoose.Types.ObjectId(req.body.category)
      : req.body.category;
  }

  // Handle relatedCategories - convert to ObjectId array if provided
  if (req.body.relatedCategories !== undefined) {
    if (Array.isArray(req.body.relatedCategories)) {
      updateData.relatedCategories = req.body.relatedCategories.map(id =>
        typeof id === 'string' ? new mongoose.Types.ObjectId(id) : id
      );
    } else {
      updateData.relatedCategories = req.body.relatedCategories;
    }
  }

  // Set status to pending for admin approval if product was inactive and core fields changed
  if (product.status === 'inactive') {
    const coreFields = ['name', 'brand', 'description', 'gender', 'basePrice', 'category'];
    const hasCoreFieldChanges = coreFields.some(field => updateData[field] !== undefined);

    if (hasCoreFieldChanges) {
      updateData.status = 'pending';

      // Update creator's product counts
      await Creator.findByIdAndUpdate(req.user.id, {
        $inc: { 'metrics.pendingProducts': 1 }
      });
    }
  }

  const updatedProduct = await Product.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true
  }).populate('category', 'name description').populate('relatedCategories', 'name description');

  res.status(200).json({
    status: 'success',
    data: {
      product: updatedProduct
    }
  });
});

/**
 * Update product specifications
 * @route PATCH /api/v1/creators/products/:id/specifications
 * @access Private (Creator only)
 */
exports.updateSpecifications = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const product = await Product.findOne({ _id: id, creator: req.user.id });
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Active products cannot have specifications changed
  if (product.status === 'active') {
    return next(new AppError('Specifications cannot be updated for active products', 400));
  }

  // Check if request body exists and has data
  if (!req.body || Object.keys(req.body).length === 0) {
    return next(new AppError('Please provide data to update', 400));
  }

  // Build specifications object with only provided fields
  const specifications = {};

  // Specification fields - only update if provided
  if (req.body.mainMaterial !== undefined) specifications.mainMaterial = req.body.mainMaterial;
  if (req.body.dressStyle !== undefined) specifications.dressStyle = req.body.dressStyle;
  if (req.body.pantType !== undefined) specifications.pantType = req.body.pantType;
  if (req.body.skirtType !== undefined) specifications.skirtType = req.body.skirtType;
  if (req.body.mensPantSize !== undefined) specifications.mensPantSize = req.body.mensPantSize;
  if (req.body.fitType !== undefined) specifications.fitType = req.body.fitType;
  if (req.body.pattern !== undefined) specifications.pattern = req.body.pattern;
  if (req.body.closure !== undefined) specifications.closure = req.body.closure;
  if (req.body.neckline !== undefined) specifications.neckline = req.body.neckline;
  if (req.body.sleeveLength !== undefined) specifications.sleeveLength = req.body.sleeveLength;
  if (req.body.waistline !== undefined) specifications.waistline = req.body.waistline;
  if (req.body.hemline !== undefined) specifications.hemline = req.body.hemline;

  // // Validate fitType enum if provided
  // if (specifications.fitType) {
  //   const validFitTypes = ['Slim', 'Regular', 'Loose', 'Oversized', 'Tailored', 'Skinny', 'Straight', 'Relaxed'];
  //   if (!validFitTypes.includes(specifications.fitType)) {
  //     return next(new AppError(`Invalid fit type. Must be one of: ${validFitTypes.join(', ')}`, 400));
  //   }
  // }

  const updatedProduct = await Product.findByIdAndUpdate(
    id,
    { specifications },
    {
      new: true,
      runValidators: true
    }
  ).populate('category', 'name description').populate('relatedCategories', 'name description');

  res.status(200).json({
    status: 'success',
    data: {
      product: updatedProduct
    }
  });
});

/**
 * Update product SEO information
 * @route PATCH /api/v1/creators/products/:id/seo
 * @access Private (Creator only)
 */
exports.updateSEO = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const product = await Product.findOne({ _id: id, creator: req.user.id });
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Check if request body exists and has data
  if (!req.body || Object.keys(req.body).length === 0) {
    return next(new AppError('Please provide data to update', 400));
  }

  // Build SEO object with only provided fields
  const seo = {};

  // SEO fields - only update if provided
  if (req.body.metaTitle !== undefined) seo.metaTitle = req.body.metaTitle;
  if (req.body.metaDescription !== undefined) seo.metaDescription = req.body.metaDescription;
  if (req.body.keywords !== undefined) {
    // Validate keywords array if provided
    if (!Array.isArray(req.body.keywords)) {
      return next(new AppError('Keywords must be an array', 400));
    }
    seo.keywords = req.body.keywords;
  }

  const updatedProduct = await Product.findByIdAndUpdate(
    id,
    { seo },
    {
      new: true,
      runValidators: true
    }
  ).populate('category', 'name description').populate('relatedCategories', 'name description');

  res.status(200).json({
    status: 'success',
    data: {
      product: updatedProduct
    }
  });
});

/**
 * Get product variations
 * @route GET /api/v1/creators/products/:id/variations
 * @access Private (Creator only)
 */
exports.getVariations = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const product = await Product.findOne({ _id: id, creator: req.user.id }).select('variations');
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    results: product.variations.length,
    data: {
      variations: product.variations
    }
  });
});

/**
 * Add a new variation to a product
 * @route POST /api/v1/creators/products/:id/variations
 * @access Private (Creator only)
 */
exports.addVariation = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  const product = await Product.findOne({ _id: id, creator: req.user.id });
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Validate required fields
  const requiredFields = ['color', 'size', 'quantity', 'price'];
  for (const field of requiredFields) {
    if (!req.body[field] && req.body[field] !== 0) {
      return next(new AppError(`Please provide ${field} for the variation`, 400));
    }
  }

  // Validate quantity and price
  if (req.body.quantity < 0) {
    return next(new AppError('Quantity cannot be negative', 400));
  }

  if (req.body.price < 0) {
    return next(new AppError('Price cannot be negative', 400));
  }

  // Validate sale price if provided
  if (req.body.salePrice) {
    if (req.body.salePrice >= req.body.price) {
      return next(new AppError('Sale price must be lower than regular price', 400));
    }

    // Validate sale dates if sale price is provided
    if (!req.body.saleStartDate || !req.body.saleEndDate) {
      return next(new AppError('Sale start date and end date are required when sale price is provided', 400));
    }

    const startDate = new Date(req.body.saleStartDate);
    const endDate = new Date(req.body.saleEndDate);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return next(new AppError('Invalid sale dates', 400));
    }

    if (startDate >= endDate) {
      return next(new AppError('Sale end date must be after sale start date', 400));
    }
  }

  // Validate images if provided
  if (req.body.images && !Array.isArray(req.body.images)) {
    return next(new AppError('Variation images must be an array', 400));
  }

  // Check for duplicate color-size combination
  const existingVariation = product.variations.find(
    v => v.color.toLowerCase() === req.body.color.toLowerCase() &&
         v.size.toLowerCase() === req.body.size.toLowerCase()
  );

  if (existingVariation) {
    return next(new AppError('A variation with this color and size combination already exists', 400));
  }

  // Add the new variation
  product.variations.push(req.body);
  await product.save();

  // Populate and return updated product
  const updatedProduct = await Product.findById(id)
    .populate('category', 'name description')
    .populate('relatedCategories', 'name description');

  res.status(201).json({
    status: 'success',
    data: {
      product: updatedProduct
    }
  });
});

/**
 * Update a specific variation
 * @route PATCH /api/v1/creators/products/:id/variations/:variationId
 * @access Private (Creator only)
 */
exports.updateVariation = catchAsync(async (req, res, next) => {
  const { id, variationId } = req.params;

  const product = await Product.findOne({ _id: id, creator: req.user.id });
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Find the variation
  const variation = product.variations.id(variationId);
  if (!variation) {
    return next(new AppError('No variation found with that ID', 404));
  }

  // For active products, only allow updating specific variation fields
  if (product.status === 'active') {
    const allowedVariationFields = ['price', 'salePrice', 'saleStartDate', 'saleEndDate', 'quantity'];
    const providedFields = Object.keys(req.body);
    const disallowedVariationFields = providedFields.filter(field => !allowedVariationFields.includes(field));

    if (disallowedVariationFields.length > 0) {
      return next(new AppError(`For active products, only ${allowedVariationFields.join(', ')} can be updated in variations. Cannot update: ${disallowedVariationFields.join(', ')}`, 400));
    }
  }

  // Validate quantity if provided
  if (req.body.quantity !== undefined && req.body.quantity < 0) {
    return next(new AppError('Quantity cannot be negative', 400));
  }

  // Validate price if provided
  if (req.body.price !== undefined && req.body.price < 0) {
    return next(new AppError('Price cannot be negative', 400));
  }

  // Validate sale price if provided
  if (req.body.salePrice !== undefined) {
    const price = req.body.price || variation.price;
    if (req.body.salePrice >= price) {
      return next(new AppError('Sale price must be lower than regular price', 400));
    }

    // Ensure sale dates are provided when sale price is updated
    const saleStartDate = req.body.saleStartDate || variation.saleStartDate;
    const saleEndDate = req.body.saleEndDate || variation.saleEndDate;

    if (!saleStartDate || !saleEndDate) {
      return next(new AppError('Sale start date and end date are required when sale price is provided', 400));
    }

    const startDate = new Date(saleStartDate);
    const endDate = new Date(saleEndDate);

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return next(new AppError('Invalid sale dates', 400));
    }

    if (startDate >= endDate) {
      return next(new AppError('Sale end date must be after sale start date', 400));
    }
  }

  // Validate images if provided
  if (req.body.images && !Array.isArray(req.body.images)) {
    return next(new AppError('Variation images must be an array', 400));
  }

  // Check for duplicate color-size combination if color or size is being updated
  if (req.body.color || req.body.size) {
    const newColor = req.body.color || variation.color;
    const newSize = req.body.size || variation.size;

    const existingVariation = product.variations.find(
      v => v._id.toString() !== variationId &&
           v.color.toLowerCase() === newColor.toLowerCase() &&
           v.size.toLowerCase() === newSize.toLowerCase()
    );

    if (existingVariation) {
      return next(new AppError('A variation with this color and size combination already exists', 400));
    }
  }

  // Update the variation
  Object.keys(req.body).forEach(key => {
    variation[key] = req.body[key];
  });

  await product.save();

  // Populate and return updated product
  const updatedProduct = await Product.findById(id)
    .populate('category', 'name description')
    .populate('relatedCategories', 'name description');

  res.status(200).json({
    status: 'success',
    data: {
      product: updatedProduct
    }
  });
});

/**
 * Delete a specific variation
 * @route DELETE /api/v1/creators/products/:id/variations/:variationId
 * @access Private (Creator only)
 */
exports.deleteVariation = catchAsync(async (req, res, next) => {
  const { id, variationId } = req.params;

  const product = await Product.findOne({ _id: id, creator: req.user.id });
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Find the variation
  const variation = product.variations.id(variationId);
  if (!variation) {
    return next(new AppError('No variation found with that ID', 404));
  }

  // Check if this is the last variation
  if (product.variations.length === 1) {
    return next(new AppError('Cannot delete the last variation. A product must have at least one variation', 400));
  }

  // For active products, don't allow variation deletion
  if (product.status === 'active') {
    return next(new AppError('Cannot delete variations from active products', 400));
  }

  // Remove the variation
  variation.deleteOne();
  await product.save();

  // Populate and return updated product
  const updatedProduct = await Product.findById(id)
    .populate('category', 'name description')
    .populate('relatedCategories', 'name description');

  res.status(200).json({
    status: 'success',
    data: {
      product: updatedProduct
    }
  });
});

/**
 * Get product reviews
 * @route GET /api/v1/creators/products/:id/reviews
 * @access Private (Creator only)
 */
exports.getProductReviews = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // Verify product belongs to creator
  const product = await Product.findOne({ _id: id, creator: req.user.id }).select('_id');
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Build query for reviews
  const queryObj = { product: id };

  // Filter by rating if provided
  if (req.query.rating) {
    const rating = parseInt(req.query.rating);
    if (rating >= 1 && rating <= 5) {
      queryObj.rating = rating;
    }
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 10;
  const skip = (page - 1) * limit;

  // Get reviews with pagination
  const reviews = await Review.find(queryObj)
    .sort('-createdAt')
    .skip(skip)
    .limit(limit)
    .populate('user', 'name photo')
    .populate('order', 'orderNumber createdAt');

  // Get total count for pagination
  const total = await Review.countDocuments(queryObj);

  // Get rating distribution
  const ratingStats = await Review.aggregate([
    { $match: { product: new mongoose.Types.ObjectId(id) } },
    {
      $group: {
        _id: '$rating',
        count: { $sum: 1 }
      }
    },
    { $sort: { _id: -1 } }
  ]);

  // Format rating distribution
  const ratingDistribution = {};
  for (let i = 1; i <= 5; i++) {
    ratingDistribution[i] = 0;
  }
  ratingStats.forEach(stat => {
    ratingDistribution[stat._id] = stat.count;
  });

  res.status(200).json({
    status: 'success',
    results: reviews.length,
    total,
    page,
    limit,
    data: {
      reviews,
      ratingDistribution
    }
  });
});

/**
 * Get product sales history
 * @route GET /api/v1/creators/products/:id/sales
 * @access Private (Creator only)
 */
exports.getProductSales = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // Verify product belongs to creator
  const product = await Product.findOne({ _id: id, creator: req.user.id }).select('_id name');
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Build date filter based on period
  let dateFilter = {};
  const { period } = req.query;
  const now = new Date();

  switch (period) {
    case 'today':
      dateFilter = {
        createdAt: {
          $gte: new Date(now.getFullYear(), now.getMonth(), now.getDate()),
          $lt: new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1)
        }
      };
      break;
    case 'week':
      const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
      dateFilter = { createdAt: { $gte: weekStart } };
      break;
    case 'month':
      dateFilter = {
        createdAt: {
          $gte: new Date(now.getFullYear(), now.getMonth(), 1)
        }
      };
      break;
    case 'year':
      dateFilter = {
        createdAt: {
          $gte: new Date(now.getFullYear(), 0, 1)
        }
      };
      break;
    default:
      // Last 30 days
      dateFilter = {
        createdAt: {
          $gte: new Date(now.setDate(now.getDate() - 30))
        }
      };
  }

  // Get sales data
  const salesData = await Order.aggregate([
    {
      $match: {
        ...dateFilter,
        status: { $in: ['completed', 'delivered'] },
        'items.product': new mongoose.Types.ObjectId(id),
        'items.creator': req.user._id
      }
    },
    {
      $unwind: '$items'
    },
    {
      $match: {
        'items.product': new mongoose.Types.ObjectId(id),
        'items.creator': req.user._id
      }
    },
    {
      $group: {
        _id: {
          $dateToString: {
            format: period === 'today' ? '%H:00' :
                   period === 'week' ? '%Y-%m-%d' :
                   period === 'month' ? '%Y-%m-%d' : '%Y-%m',
            date: '$createdAt'
          }
        },
        totalSales: { $sum: '$items.quantity' },
        totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
        orderCount: { $sum: 1 }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);

  // Get summary statistics
  const summary = await Order.aggregate([
    {
      $match: {
        ...dateFilter,
        status: { $in: ['completed', 'delivered'] },
        'items.product': new mongoose.Types.ObjectId(id),
        'items.creator': req.user._id
      }
    },
    {
      $unwind: '$items'
    },
    {
      $match: {
        'items.product': new mongoose.Types.ObjectId(id),
        'items.creator': req.user._id
      }
    },
    {
      $group: {
        _id: null,
        totalQuantitySold: { $sum: '$items.quantity' },
        totalRevenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } },
        totalOrders: { $sum: 1 },
        averageOrderValue: { $avg: { $multiply: ['$items.price', '$items.quantity'] } }
      }
    }
  ]);

  res.status(200).json({
    status: 'success',
    data: {
      product: {
        id: product._id,
        name: product.name
      },
      period: period || 'last30days',
      summary: summary[0] || {
        totalQuantitySold: 0,
        totalRevenue: 0,
        totalOrders: 0,
        averageOrderValue: 0
      },
      salesData
    }
  });
});

/**
 * Get product promotions
 * @route GET /api/v1/creators/products/:id/promotions
 * @access Private (Creator only)
 */
exports.getProductPromotions = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // Verify product belongs to creator
  const product = await Product.findOne({ _id: id, creator: req.user.id }).select('promotions');
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Get active admin promotions that this product participates in
  const activePromotions = await Promotion.find({
    'participants.products.product': id,
    'participants.creator': req.user.id,
    'participants.status': 'approved',
    isActive: true,
    endDate: { $gte: new Date() }
  }).select('name code type startDate endDate description banners');

  // Get product's promotion entries (tracks participation in admin promotions)
  const productPromotions = product.promotions.filter(promo =>
    promo.isActive && new Date() <= promo.endDate
  );

  // Populate promotion details for product promotions
  const populatedProductPromotions = await Promise.all(
    productPromotions.map(async (promo) => {
      const promotionDetails = await Promotion.findById(promo.promotion).select('name code type description');
      return {
        ...promo.toObject(),
        promotionDetails
      };
    })
  );

  res.status(200).json({
    status: 'success',
    data: {
      productId: id,
      activeAdminPromotions: activePromotions,
      productPromotions: populatedProductPromotions,
      totalActivePromotions: activePromotions.length
    }
  });
});

/**
 * Join product to an admin promotion
 * @route POST /api/v1/creators/products/:id/promotions
 * @access Private (Creator only)
 */
exports.joinProductPromotion = catchAsync(async (req, res, next) => {
  const { id } = req.params;
  const { promotionId, discountValue, discountType, promoStock } = req.body;

  // Validate required fields
  if (!promotionId || !discountValue || !discountType || !promoStock) {
    return next(new AppError('Please provide promotionId, discountValue, discountType, and promoStock', 400));
  }

  const product = await Product.findOne({ _id: id, creator: req.user.id });
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Find the admin promotion
  const promotion = await Promotion.findById(promotionId);
  if (!promotion) {
    return next(new AppError('No promotion found with that ID', 404));
  }

  // Check if promotion is active and not expired
  if (!promotion.isActive || promotion.isExpired) {
    return next(new AppError('This promotion is not active or has expired', 400));
  }

  // Check if promotion hasn't started yet (can only join before it starts)
  if (new Date() >= promotion.startDate) {
    return next(new AppError('Cannot join a promotion that has already started', 400));
  }

  // Validate discount type
  if (!['percentage', 'fixed'].includes(discountType)) {
    return next(new AppError('Discount type must be either "percentage" or "fixed"', 400));
  }

  // Validate discount value
  if (discountValue <= 0) {
    return next(new AppError('Discount value must be greater than 0', 400));
  }

  if (discountType === 'percentage' && discountValue > 100) {
    return next(new AppError('Percentage discount cannot exceed 100%', 400));
  }

  // Check if product meets promotion criteria
  const { minDiscount, maxDiscount, categories } = promotion.criteria;

  // Calculate discount percentage for validation
  const minPrice = product.normalMinPrice || product.basePrice;
  const discountPercentage = discountType === 'percentage' ?
    discountValue :
    ((discountValue / minPrice) * 100);

  if (discountPercentage < minDiscount) {
    return next(new AppError(`Discount must be at least ${minDiscount}%`, 400));
  }

  if (maxDiscount && discountPercentage > maxDiscount) {
    return next(new AppError(`Discount cannot exceed ${maxDiscount}%`, 400));
  }

  // Check category criteria if specified
  if (categories && categories.length > 0) {
    const productCategoryIds = [product.category, ...(product.relatedCategories || [])].map(cat => cat.toString());
    const promotionCategoryIds = categories.map(cat => cat.toString());

    const hasMatchingCategory = productCategoryIds.some(catId =>
      promotionCategoryIds.includes(catId)
    );

    if (!hasMatchingCategory) {
      return next(new AppError('Product category does not match promotion criteria', 400));
    }
  }

  // Check if product is already in this promotion
  const existingPromotion = product.promotions.find(promo =>
    promo.promotion && promo.promotion.toString() === promotionId
  );

  if (existingPromotion) {
    return next(new AppError('Product is already participating in this promotion', 400));
  }

  // Add product to promotion in the product model
  const productPromotion = {
    promotion: promotionId,
    discountValue,
    discountType,
    promoStock,
    startDate: promotion.startDate,
    endDate: promotion.endDate,
    isActive: true
  };

  product.promotions.push(productPromotion);
  await product.save();

  // Also add to the main promotion model (this should ideally be done through the promotion controller)
  const existingParticipant = promotion.participants.find(p =>
    p.creator.toString() === req.user.id
  );

  if (existingParticipant) {
    // Add product to existing participant
    existingParticipant.products.push({
      product: id,
      discountValue,
      discountType,
      promoStock
    });
  } else {
    // Add new participant
    promotion.participants.push({
      creator: req.user.id,
      products: [{
        product: id,
        discountValue,
        discountType,
        promoStock
      }],
      status: 'pending'
    });
  }

  await promotion.save();

  res.status(201).json({
    status: 'success',
    message: 'Product successfully added to promotion. Awaiting admin approval.',
    data: {
      productId: id,
      promotionId,
      status: 'pending'
    }
  });
});

/**
 * Update product promotion
 * @route PATCH /api/v1/creators/products/:id/promotions/:promotionId
 * @access Private (Creator only)
 */
exports.updateProductPromotion = catchAsync(async (req, res, next) => {
  const { id, promotionId } = req.params;

  const product = await Product.findOne({ _id: id, creator: req.user.id });
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Find the promotion
  const promotion = product.promotions.id(promotionId);
  if (!promotion) {
    return next(new AppError('No promotion found with that ID', 404));
  }

  // Don't allow updating if promotion has already started
  if (new Date() >= promotion.startDate) {
    return next(new AppError('Cannot update a promotion that has already started', 400));
  }

  // Validate discount type if provided
  if (req.body.discountType && !['percentage', 'fixed'].includes(req.body.discountType)) {
    return next(new AppError('Discount type must be either "percentage" or "fixed"', 400));
  }

  // Validate discount value if provided
  if (req.body.discountValue !== undefined) {
    if (req.body.discountValue <= 0) {
      return next(new AppError('Discount value must be greater than 0', 400));
    }

    const discountType = req.body.discountType || promotion.discountType;
    if (discountType === 'percentage' && req.body.discountValue > 100) {
      return next(new AppError('Percentage discount cannot exceed 100%', 400));
    }
  }

  // Validate dates if provided
  if (req.body.startDate || req.body.endDate) {
    const startDate = req.body.startDate ? new Date(req.body.startDate) : promotion.startDate;
    const endDate = req.body.endDate ? new Date(req.body.endDate) : promotion.endDate;

    if (req.body.startDate && isNaN(startDate.getTime())) {
      return next(new AppError('Invalid start date', 400));
    }

    if (req.body.endDate && isNaN(endDate.getTime())) {
      return next(new AppError('Invalid end date', 400));
    }

    if (startDate >= endDate) {
      return next(new AppError('End date must be after start date', 400));
    }

    if (startDate < new Date()) {
      return next(new AppError('Start date cannot be in the past', 400));
    }
  }

  // Validate promo stock if provided
  if (req.body.promoStock !== undefined && req.body.promoStock <= 0) {
    return next(new AppError('Promotional stock must be greater than 0', 400));
  }

  // Update only allowed fields for promotion participation
  if (req.body.discountValue !== undefined) {
    promotion.discountValue = req.body.discountValue;
  }
  if (req.body.discountType) {
    promotion.discountType = req.body.discountType;
  }
  if (req.body.promoStock !== undefined) {
    promotion.promoStock = req.body.promoStock;
  }

  await product.save();

  res.status(200).json({
    status: 'success',
    message: 'Promotion participation updated successfully',
    data: {
      productId: id,
      promotionId
    }
  });
});

/**
 * Leave/Remove product from promotion
 * @route DELETE /api/v1/creators/products/:id/promotions/:promotionId
 * @access Private (Creator only)
 */
exports.leaveProductPromotion = catchAsync(async (req, res, next) => {
  const { id, promotionId } = req.params;

  const product = await Product.findOne({ _id: id, creator: req.user.id });
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Find the product's promotion entry
  const promotionIndex = product.promotions.findIndex(promo =>
    promo.promotion && promo.promotion.toString() === promotionId
  );

  if (promotionIndex === -1) {
    return next(new AppError('Product is not participating in this promotion', 404));
  }

  // Get the admin promotion details
  const adminPromotion = await Promotion.findById(promotionId);
  if (!adminPromotion) {
    return next(new AppError('Promotion not found', 404));
  }

  // Don't allow leaving if promotion has already started
  if (new Date() >= adminPromotion.startDate) {
    return next(new AppError('Cannot leave a promotion that has already started', 400));
  }

  // Remove from product promotions array
  product.promotions.splice(promotionIndex, 1);
  await product.save();

  // Also remove from the main promotion model
  const participant = adminPromotion.participants.find(p =>
    p.creator.toString() === req.user.id
  );

  if (participant) {
    const productIndex = participant.products.findIndex(p =>
      p.product.toString() === id
    );

    if (productIndex !== -1) {
      participant.products.splice(productIndex, 1);

      // If no more products, remove participant entirely
      if (participant.products.length === 0) {
        const participantIndex = adminPromotion.participants.findIndex(p =>
          p.creator.toString() === req.user.id
        );
        adminPromotion.participants.splice(participantIndex, 1);
      }
    }
  }

  await adminPromotion.save();

  res.status(200).json({
    status: 'success',
    message: 'Product successfully removed from promotion',
    data: null
  });
});

