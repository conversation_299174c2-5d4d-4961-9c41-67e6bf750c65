/**
 * Test script to verify middleware processing order for product images update
 * This simulates the middleware chain to ensure proper handling of form data + file uploads
 */

// Simulate the middleware processing
function simulateMiddlewareChain() {
  console.log('🧪 Testing Middleware Processing Chain\n');

  // Initial request simulation
  let req = {
    body: {},
    files: []
  };

  console.log('📥 Initial Request:');
  console.log('   Body:', req.body);
  console.log('   Files:', req.files);
  console.log('');

  // Step 1: Simulate form data parsing (what multer does)
  console.log('1️⃣ Multer processes form data:');
  req.body = {
    'images[]': [
      'https://res.cloudinary.com/djxjevjcm/image/upload/v1751210309/everyfash/products/recruitment-1751210307587-93019793.jpg',
      'https://res.cloudinary.com/djxjevjcm/image/upload/v1751210320/everyfash/products/Virginie-1751210310378-137236271.jpg'
    ]
  };
  req.files = [
    {
      fieldname: 'productImages',
      path: 'https://res.cloudinary.com/djxjevjcm/image/upload/v1751210400/everyfash/products/new-uploaded-image.jpg'
    }
  ];
  console.log('   Body after multer:', req.body);
  console.log('   Files after multer:', req.files);
  console.log('');

  // Step 2: Simulate processCloudinaryFiles middleware (UNCHANGED - backward compatible)
  console.log('2️⃣ processCloudinaryFiles middleware:');
  if (req.files && Array.isArray(req.files)) {
    const fieldName = req.files[0]?.fieldname;
    const fileUrls = req.files.map(file => file.path);

    if (fieldName === 'productImages') {
      // ORIGINAL BEHAVIOR: Set req.body.images (for backward compatibility)
      req.body.images = fileUrls;
      console.log('   ✅ Set req.body.images (original behavior):', fileUrls);
    }
  }
  console.log('   Body after processCloudinaryFiles:', req.body);
  console.log('');

  // Step 2.5: Simulate NEW processProductImagesUpdate middleware
  console.log('2️⃣.5 processProductImagesUpdate middleware (NEW):');
  if (req.files && Array.isArray(req.files)) {
    const fieldName = req.files[0]?.fieldname;
    if (fieldName === 'productImages') {
      const fileUrls = req.files.map(file => file.path);
      req.body.uploadedImages = fileUrls;
      // Remove the images field to avoid confusion
      delete req.body.images;
      console.log('   ✅ Moved uploaded files to uploadedImages:', fileUrls);
      console.log('   ✅ Removed req.body.images to avoid overwriting existing');
    }
  }
  console.log('   Body after processProductImagesUpdate:', req.body);
  console.log('');

  // Step 3: Simulate processNestedFormData middleware
  console.log('3️⃣ processNestedFormData middleware:');
  const arrayFields = {};
  Object.keys(req.body).forEach(key => {
    if (key.endsWith('[]')) {
      const fieldName = key.slice(0, -2);
      if (!arrayFields[fieldName]) {
        arrayFields[fieldName] = [];
      }
      // Handle both single values and arrays
      if (Array.isArray(req.body[key])) {
        arrayFields[fieldName].push(...req.body[key]);
      } else {
        arrayFields[fieldName].push(req.body[key]);
      }
    }
  });

  // Add processed arrays to the body
  Object.keys(arrayFields).forEach(key => {
    req.body[key] = arrayFields[key];
  });
  console.log('   ✅ Processed images[] into images array:', req.body.images);
  console.log('   Body after processNestedFormData:', req.body);
  console.log('');

  // Step 4: Simulate controller logic
  console.log('4️⃣ Controller processes final data:');
  
  // Get existing images to keep
  let existingImages = [];
  if (req.body.images && Array.isArray(req.body.images)) {
    existingImages = req.body.images;
    console.log('   ✅ Existing images to keep:', existingImages);
  }

  // Get uploaded images
  let uploadedImages = [];
  if (req.body.uploadedImages && Array.isArray(req.body.uploadedImages)) {
    uploadedImages = req.body.uploadedImages;
    console.log('   ✅ New uploaded images:', uploadedImages);
  }

  // Build final images array
  const finalImages = [...existingImages, ...uploadedImages];
  console.log('   ✅ Final images array:', finalImages);
  console.log('');

  // Verify the result
  console.log('🎯 Result Verification:');
  console.log(`   Total images: ${finalImages.length}`);
  console.log(`   Kept existing: ${existingImages.length}`);
  console.log(`   Added new: ${uploadedImages.length}`);
  
  const expectedTotal = 3; // 2 existing + 1 uploaded
  if (finalImages.length === expectedTotal) {
    console.log('   ✅ SUCCESS: All images preserved and new ones added!');
  } else {
    console.log(`   ❌ FAILED: Expected ${expectedTotal} images, got ${finalImages.length}`);
  }
}

// Run the simulation
if (require.main === module) {
  simulateMiddlewareChain();
}

module.exports = { simulateMiddlewareChain };
