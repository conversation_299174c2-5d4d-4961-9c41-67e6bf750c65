const mongoose = require('mongoose');
const Bale = require('../../models/bale.model');
const Product = require('../../models/product.model');
const Category = require('../../models/category.model');
const { Creator } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get all bales for the logged-in creator
 * @route GET /api/v1/creators/bales
 * @access Private (Creator only)
 */
exports.getMyBales = catchAsync(async (req, res, next) => {
  // Build query
  const queryObj = { ...req.query, creator: req.user.id };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);
  
  let query = Bale.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { country: searchRegex }
      ]
    });
  }

  // Filter by status if specified
  if (req.query.status) {
    query = query.find({ status: req.query.status });
  }

  // Clone the query for counting total
  const countQuery = { ...JSON.parse(queryStr) };
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    countQuery.$or = [
      { name: searchRegex },
      { description: searchRegex },
      { country: searchRegex }
    ];
  }
  if (req.query.status) {
    countQuery.status = req.query.status;
  }
  countQuery.creator = req.user.id;
  
  // Count total before applying pagination
  const total = await Bale.countDocuments(countQuery);

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  // Pagination
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query with population
  const bales = await query
    .populate({
      path: 'category',
      select: 'name description'
    })
    .populate({
      path: 'relatedCategories',
      select: 'name description'
    });
    
  // Transform category data to extract the actual category name and path
  bales.forEach(bale => {
    if (bale.category) {
      // Extract the full path as an array
      const pathArray = bale.category.name.split(' > ');
      const actualCategoryName = pathArray[pathArray.length - 1];

      // Add the path array and update the name
      bale.category._doc.pathArray = pathArray;
      bale.category._doc.name = actualCategoryName;
    }

    if (bale.relatedCategories && bale.relatedCategories.length > 0) {
      bale.relatedCategories.forEach(category => {
        if (category) {
          // Extract the full path as an array
          const pathArray = category.name.split(' > ');
          const actualCategoryName = pathArray[pathArray.length - 1];

          // Add the path array and update the name
          category._doc.pathArray = pathArray;
          category._doc.name = actualCategoryName;
        }
      });
    }
  });

  res.status(200).json({
    status: 'success',
    results: bales.length,
    total,
    page,
    limit,
    data: {
      bales
    }
  });
});

/**
 * Get bale by ID for the logged-in creator
 * @route GET /api/v1/creators/bales/:id
 * @access Private (Creator only)
 */
exports.getMyBale = catchAsync(async (req, res, next) => {
  const bale = await Bale.findOne({
    _id: req.params.id,
    creator: req.user.id
  })
  .populate({
    path: 'category',
    select: 'name description'
  })
  .populate({
    path: 'relatedCategories',
    select: 'name description'
  });

  if (!bale) {
    return next(new AppError('No bale found with that ID', 404));
  }
  
  // Transform category data to extract the actual category name and path
  if (bale.category) {
    // Extract the full path as an array
    const pathArray = bale.category.name.split(' > ');
    const actualCategoryName = pathArray[pathArray.length - 1];

    // Add the path array and update the name
    bale.category._doc.pathArray = pathArray;
    bale.category._doc.name = actualCategoryName;
  }

  if (bale.relatedCategories && bale.relatedCategories.length > 0) {
    bale.relatedCategories.forEach(category => {
      if (category) {
        // Extract the full path as an array
        const pathArray = category.name.split(' > ');
        const actualCategoryName = pathArray[pathArray.length - 1];

        // Add the path array and update the name
        category._doc.pathArray = pathArray;
        category._doc.name = actualCategoryName;
      }
    });
  }

  res.status(200).json({
    status: 'success',
    data: {
      bale
    }
  });
});

/**
 * Create a new bale
 * @route POST /api/v1/creators/bales
 * @access Private (Creator only)
 */
exports.createBale = catchAsync(async (req, res, next) => {
  // Add creator to req.body
  req.body.creator = req.user.id;

  // Validate required fields
  const requiredFields = ['name', 'description', 'basePrice', 'country', 'totalItems', 'weight'];
  for (const field of requiredFields) {
    if (!req.body[field]) {
      return next(new AppError(`Please provide ${field}`, 400));
    }
  }

  // Handle images
  if (!req.body.images || !Array.isArray(req.body.images) || req.body.images.length === 0) {
    return next(new AppError('Please provide at least one bale image', 400));
  }

  // Validate basePrice
  if (req.body.basePrice < 0) {
    return next(new AppError('Base price cannot be negative', 400));
  }

  // Validate totalItems
  if (req.body.totalItems < 1) {
    return next(new AppError('Total items must be at least 1', 400));
  }

  // Validate weight
  if (req.body.weight <= 0) {
    return next(new AppError('Weight must be greater than 0', 400));
  }

  // Validate country
  if (!req.body.country || typeof req.body.country !== 'string' || req.body.country.trim() === '') {
    return next(new AppError('Please provide a valid country of origin', 400));
  }

  // Validate condition if provided
  if (req.body.condition) {
    const validConditions = ['New', 'Like New', 'Good', 'Fair', 'Poor'];
    if (!validConditions.includes(req.body.condition)) {
      return next(new AppError(`Condition must be one of: ${validConditions.join(', ')}`, 400));
    }
  }

  // Validate category if provided
  if (req.body.category && !mongoose.Types.ObjectId.isValid(req.body.category)) {
    return next(new AppError('Please provide a valid category ID', 400));
  }

  // Validate related categories if provided
  if (req.body.relatedCategories) {
    if (!Array.isArray(req.body.relatedCategories)) {
      return next(new AppError('Related categories must be an array', 400));
    }

    for (const categoryId of req.body.relatedCategories) {
      if (!mongoose.Types.ObjectId.isValid(categoryId)) {
        return next(new AppError('Please provide valid related category IDs', 400));
      }
    }
  }

  // Validate variations if provided
  if (req.body.variations) {
    if (!Array.isArray(req.body.variations) || req.body.variations.length === 0) {
      return next(new AppError('Please provide at least one bale variation', 400));
    }

    // Validate each variation
    for (const variation of req.body.variations) {
      // Check required fields
      const variationRequiredFields = ['size', 'quantity', 'price'];
      for (const field of variationRequiredFields) {
        if (!variation[field] && variation[field] !== 0) {
          return next(new AppError(`Please provide ${field} for each variation`, 400));
        }
      }

      // Validate quantity
      if (variation.quantity < 0) {
        return next(new AppError('Quantity cannot be negative', 400));
      }

      // Validate price
      if (variation.price < 0) {
        return next(new AppError('Price cannot be negative', 400));
      }


      // Validate sale price if provided
      if (variation.salePrice) {
        if (variation.salePrice >= variation.price) {
          return next(new AppError('Sale price must be lower than regular price', 400));
        }

        // Validate sale dates if sale price is provided
        if (!variation.saleStartDate || !variation.saleEndDate) {
          return next(new AppError('Sale start date and end date are required when sale price is provided', 400));
        }

        const startDate = new Date(variation.saleStartDate);
        const endDate = new Date(variation.saleEndDate);

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return next(new AppError('Invalid sale dates', 400));
        }

        if (startDate >= endDate) {
          return next(new AppError('Sale end date must be after sale start date', 400));
        }
      }
    }
  }

  // Validate products in the bale if provided
  if (req.body.products && req.body.products.length > 0) {
    // Check if all products have product and quantity
    for (const item of req.body.products) {
      if (!item.product || !item.quantity) {
        return next(new AppError('Each product in the bale must have a product ID and quantity', 400));
      }
      if (item.quantity < 1) {
        return next(new AppError('Product quantity must be at least 1', 400));
      }
      if (!mongoose.Types.ObjectId.isValid(item.product)) {
        return next(new AppError('Please provide valid product IDs', 400));
      }
    }

    // Check if products belong to the creator
    const productIds = req.body.products.map(item => item.product);
    const products = await Product.find({
      _id: { $in: productIds },
      creator: req.user.id
    });

    if (products.length !== productIds.length) {
      return next(new AppError('Some products do not belong to you or do not exist', 400));
    }
  }

  // Validate dimensions if provided
  if (req.body.dimensions) {
    const dimensionFields = ['length', 'width', 'height'];
    for (const field of dimensionFields) {
      if (req.body.dimensions[field] !== undefined && req.body.dimensions[field] <= 0) {
        return next(new AppError(`Dimension ${field} must be greater than 0`, 400));
      }
    }
  }

  // Validate highlights if provided
  if (req.body.highlights && !Array.isArray(req.body.highlights)) {
    return next(new AppError('Highlights must be an array', 400));
  }

  // Validate tags if provided
  if (req.body.tags && !Array.isArray(req.body.tags)) {
    return next(new AppError('Tags must be an array', 400));
  }

  // Set initial status to pending for admin approval
  req.body.status = 'pending';

  // Ensure category is a valid ObjectId
  if (req.body.category && typeof req.body.category === 'string') {
    req.body.category = new mongoose.Types.ObjectId(req.body.category);
  }

  // Ensure relatedCategories is an array of valid ObjectIds
  if (req.body.relatedCategories && Array.isArray(req.body.relatedCategories)) {
    req.body.relatedCategories = req.body.relatedCategories.map(id => {
      if (typeof id === 'string') {
        return new mongoose.Types.ObjectId(id);
      }
      return id;
    });
  }

  // Ensure products is an array of objects with valid ObjectIds
  if (req.body.products && Array.isArray(req.body.products)) {
    req.body.products = req.body.products.map(item => ({
      ...item,
      product: typeof item.product === 'string' ? new mongoose.Types.ObjectId(item.product) : item.product
    }));
  }

  // Create the bale
  const newBale = await Bale.create(req.body);

  // Update creator's bale count
  await Creator.findByIdAndUpdate(req.user.id, {
    $inc: { 'metrics.pendingBales': 1 }
  });

  res.status(201).json({
    status: 'success',
    data: {
      bale: newBale
    }
  });
});

/**
 * Update bale by ID for the logged-in creator
 * @route PATCH /api/v1/creators/bales/:id
 * @access Private (Creator only)
 */
exports.updateMyBale = catchAsync(async (req, res, next) => {
  const { id } = req.params;

  // Find bale
  const bale = await Bale.findOne({ _id: id, creator: req.user.id });
  if (!bale) return next(new AppError('No bale found with that ID', 404));

  // Special handling for active bales - only allow variations update
  if (bale.status === 'active') {
    // Only allow variations to be updated for active bales
    if (!req.body.variations) {
      return next(new AppError('For active bales, only variations can be updated', 400));
    }

    // Check if any other fields are being updated
    const allowedFieldsForActive = ['variations'];
    const attemptedFields = Object.keys(req.body);
    const disallowedFields = attemptedFields.filter(field => !allowedFieldsForActive.includes(field));
    
    if (disallowedFields.length > 0) {
      return next(new AppError(`For active bales, only variations can be updated. Cannot update: ${disallowedFields.join(', ')}`, 400));
    }

    // Handle variations update for active bales
    if (!Array.isArray(req.body.variations) || req.body.variations.length === 0) {
      return next(new AppError('Please provide at least one bale variation', 400));
    }

    const existingVariationIds = bale.variations.map(v => v._id.toString());
    const updatedVariations = [];

    for (const variation of req.body.variations) {
      if (!variation._id) {
        return next(new AppError('Cannot add new variations to active bales', 400));
      }

      if (!existingVariationIds.includes(variation._id.toString())) {
        return next(new AppError(`Variation with ID ${variation._id} not found in this bale`, 404));
      }

      const existingVariation = bale.variations.id(variation._id);
      
      // For active bales, only allow updating specific variation fields
      const allowedVariationFields = ['price', 'salePrice', 'saleStartDate', 'saleEndDate', 'quantity'];
      const providedFields = Object.keys(variation).filter(key => key !== '_id');
      const disallowedVariationFields = providedFields.filter(field => !allowedVariationFields.includes(field));
      
      if (disallowedVariationFields.length > 0) {
        return next(new AppError(`For active bales, only ${allowedVariationFields.join(', ')} can be updated in variations. Cannot update: ${disallowedVariationFields.join(', ')}`, 400));
      }

      // Validate variation fields
      if (variation.quantity !== undefined && variation.quantity < 0) {
        return next(new AppError('Quantity cannot be negative', 400));
      }
      
      if (variation.price !== undefined && variation.price < 0) {
        return next(new AppError('Price cannot be negative', 400));
      }
      
      if (variation.salePrice !== undefined) {
        if (variation.salePrice >= (variation.price || existingVariation.price)) {
          return next(new AppError('Sale price must be lower than regular price', 400));
        }
        
        // Ensure sale dates are provided when sale price is updated
        const saleStartDate = variation.saleStartDate || existingVariation.saleStartDate;
        const saleEndDate = variation.saleEndDate || existingVariation.saleEndDate;
        
        if (!saleStartDate || !saleEndDate) {
          return next(new AppError('Sale start date and end date are required when sale price is provided', 400));
        }
        
        const startDate = new Date(saleStartDate);
        const endDate = new Date(saleEndDate);
        
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
          return next(new AppError('Invalid sale dates', 400));
        }
        
        if (startDate >= endDate) {
          return next(new AppError('Sale end date must be after sale start date', 400));
        }
      }
      
      // Update allowed fields
      allowedVariationFields.forEach(field => {
        if (variation[field] !== undefined) {
          existingVariation[field] = variation[field];
        }
      });
      
      updatedVariations.push(existingVariation);
    }
    
    // Keep existing variations that weren't updated
    const variationsToKeep = bale.variations.filter(
      v => !req.body.variations.some(updateV => updateV._id && updateV._id.toString() === v._id.toString())
    );
    
    // Update the bale with only the variations
    const updatedBale = await Bale.findByIdAndUpdate(
      id, 
      { variations: [...variationsToKeep, ...updatedVariations] },
      { new: true, runValidators: true }
    );
    
    // Populate category and relatedCategories
    if (updatedBale.category) {
      const category = await Category.findById(updatedBale.category);
      if (category) {
        const pathArray = category.name.split(' > ');
        const actualCategoryName = pathArray[pathArray.length - 1];
        
        updatedBale.category = {
          _id: category._id,
          name: actualCategoryName,
          description: category.description,
          pathArray: pathArray,
          id: category._id
        };
      }
    }

    if (updatedBale.relatedCategories?.length > 0) {
      const populated = [];

      for (const id of updatedBale.relatedCategories) {
        const cat = await Category.findById(id);
        if (cat) {
          const pathArray = cat.name.split(' > ');
          const actualCategoryName = pathArray[pathArray.length - 1];
          
          populated.push({
            _id: cat._id,
            name: actualCategoryName,
            description: cat.description,
            pathArray: pathArray,
            id: cat._id
          });
        }
      }

      updatedBale.relatedCategories = populated;
    }
    
    return res.status(200).json({
      status: 'success',
      data: {
        bale: updatedBale
      }
    });
  }

  // For non-active bales, continue with the existing logic
  // Check if bale is in a state that can be updated
  if (!['draft', 'pending', 'rejected', 'inactive'].includes(bale.status)) {
    return next(new AppError(`Cannot update bale in ${bale.status} status`, 400));
  }

  // Validate basePrice if provided
  if (req.body.basePrice !== undefined && req.body.basePrice < 0) {
    return next(new AppError('Base price cannot be negative', 400));
  }

  // Validate totalItems if provided
  if (req.body.totalItems !== undefined && req.body.totalItems < 1) {
    return next(new AppError('Total items must be at least 1', 400));
  }

  // Validate weight if provided
  if (req.body.weight !== undefined && req.body.weight <= 0) {
    return next(new AppError('Weight must be greater than 0', 400));
  }

  // Validate country if provided
  if (req.body.country && (typeof req.body.country !== 'string' || req.body.country.trim() === '')) {
    return next(new AppError('Please provide a valid country of origin', 400));
  }

  // Validate condition if provided
  if (req.body.condition) {
    const validConditions = ['New', 'Like New', 'Good', 'Fair', 'Poor'];
    if (!validConditions.includes(req.body.condition)) {
      return next(new AppError(`Condition must be one of: ${validConditions.join(', ')}`, 400));
    }
  }

  // Validate category if provided
  if (req.body.category && !mongoose.Types.ObjectId.isValid(req.body.category)) {
    return next(new AppError('Please provide a valid category ID', 400));
  }

  // Validate related categories if provided
  if (req.body.relatedCategories) {
    if (!Array.isArray(req.body.relatedCategories)) {
      return next(new AppError('Related categories must be an array', 400));
    }

    for (const categoryId of req.body.relatedCategories) {
      if (!mongoose.Types.ObjectId.isValid(categoryId)) {
        return next(new AppError('Please provide valid related category IDs', 400));
      }
    }
  }

  // Handle variations update
  const updatedVariations = [];
  if (req.body.variations) {
    if (!Array.isArray(req.body.variations) || req.body.variations.length === 0) {
      return next(new AppError('Please provide at least one bale variation', 400));
    }

    const existingVariationIds = bale.variations.map(v => v._id.toString());

    for (const variation of req.body.variations) {
      if (variation._id) {
        if (!existingVariationIds.includes(variation._id.toString())) {
          return next(new AppError(`Variation with ID ${variation._id} not found in this bale`, 404));
        }

        const existingVariation = bale.variations.id(variation._id);

        Object.keys(variation).forEach(key => {
          if (key !== '_id') {
            if (key === 'quantity' && variation[key] < 0) {
              return next(new AppError('Quantity cannot be negative', 400));
            }
            if (key === 'price' && variation[key] < 0) {
              return next(new AppError('Price cannot be negative', 400));
            }
            existingVariation[key] = variation[key];
          }
        });

        updatedVariations.push(existingVariation);
      } else {
        const requiredFields = ['size', 'quantity', 'price'];
        for (const field of requiredFields) {
          if (!variation[field] && variation[field] !== 0) {
            return next(new AppError(`Please provide ${field} for each new variation`, 400));
          }
        }

        if (variation.quantity < 0) {
          return next(new AppError('Quantity cannot be negative', 400));
        }

        if (variation.price < 0) {
          return next(new AppError('Price cannot be negative', 400));
        }


        if (variation.salePrice && variation.salePrice >= variation.price) {
          return next(new AppError('Sale price must be lower than regular price', 400));
        }

        if (variation.salePrice && (!variation.saleStartDate || !variation.saleEndDate)) {
          return next(new AppError('Sale start date and end date are required when sale price is provided', 400));
        }

        if (variation.salePrice) {
          const startDate = new Date(variation.saleStartDate);
          const endDate = new Date(variation.saleEndDate);

          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            return next(new AppError('Invalid sale dates', 400));
          }

          if (startDate >= endDate) {
            return next(new AppError('Sale end date must be after sale start date', 400));
          }
        }

        updatedVariations.push(variation);
      }
    }

    // Keep existing variations that weren't updated
    const variationsToKeep = bale.variations.filter(
      v => !req.body.variations.some(updateV => updateV._id && updateV._id.toString() === v._id.toString())
    );

    req.body.variations = [...variationsToKeep, ...updatedVariations];
  }

  // Validate products in the bale if provided
  if (req.body.products && req.body.products.length > 0) {
    // Check if all products have product and quantity
    for (const item of req.body.products) {
      if (!item.product || !item.quantity) {
        return next(new AppError('Each product in the bale must have a product ID and quantity', 400));
      }
      if (item.quantity < 1) {
        return next(new AppError('Product quantity must be at least 1', 400));
      }
      if (!mongoose.Types.ObjectId.isValid(item.product)) {
        return next(new AppError('Please provide valid product IDs', 400));
      }
    }

    // Check if products belong to the creator
    const productIds = req.body.products.map(item => item.product);
    const products = await Product.find({
      _id: { $in: productIds },
      creator: req.user.id
    });

    if (products.length !== productIds.length) {
      return next(new AppError('Some products do not belong to you or do not exist', 400));
    }
  }

  // Validate dimensions if provided
  if (req.body.dimensions) {
    const dimensionFields = ['length', 'width', 'height'];
    for (const field of dimensionFields) {
      if (req.body.dimensions[field] !== undefined && req.body.dimensions[field] <= 0) {
        return next(new AppError(`Dimension ${field} must be greater than 0`, 400));
      }
    }
  }

  // Validate highlights if provided
  if (req.body.highlights && !Array.isArray(req.body.highlights)) {
    return next(new AppError('Highlights must be an array', 400));
  }

  // Validate tags if provided
  if (req.body.tags && !Array.isArray(req.body.tags)) {
    return next(new AppError('Tags must be an array', 400));
  }

  // Create a filtered body to prevent unwanted fields
  const filteredBody = {};
  const allowedFields = ['name', 'description', 'basePrice', 'category', 'relatedCategories', 'tags', 'products', 'country', 'totalItems', 'weight', 'dimensions', 'condition', 'variations', 'highlights', 'images', 'seo'];

  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      filteredBody[key] = req.body[key];
    }
  });

  // Set status to pending for admin approval if bale was inactive
  if (bale.status === 'inactive') {
    filteredBody.status = 'pending';
    
    // Update creator's bale counts
    await Creator.findByIdAndUpdate(req.user.id, {
      $inc: { 'metrics.pendingBales': 1 }
    });
  }

  // Ensure category is a valid ObjectId if provided
  if (filteredBody.category && typeof filteredBody.category === 'string') {
    filteredBody.category = new mongoose.Types.ObjectId(filteredBody.category);
  }

  // Ensure relatedCategories is an array of valid ObjectIds if provided
  if (filteredBody.relatedCategories && Array.isArray(filteredBody.relatedCategories)) {
    filteredBody.relatedCategories = filteredBody.relatedCategories.map(id => {
      if (typeof id === 'string') {
        return new mongoose.Types.ObjectId(id);
      }
      return id;
    });
  }

  // Ensure products is an array of objects with valid ObjectIds if provided
  if (filteredBody.products && Array.isArray(filteredBody.products)) {
    filteredBody.products = filteredBody.products.map(item => ({
      ...item,
      product: typeof item.product === 'string' ? new mongoose.Types.ObjectId(item.product) : item.product
    }));
  }

  // Update the bale
  const updatedBale = await Bale.findByIdAndUpdate(id, filteredBody, {
    new: true,
    runValidators: true
  });

  // Populate category and relatedCategories
  if (updatedBale.category) {
    const category = await Category.findById(updatedBale.category);
    if (category) {
      const pathArray = category.name.split(' > ');
      const actualCategoryName = pathArray[pathArray.length - 1];
      
      updatedBale.category = {
        _id: category._id,
        name: actualCategoryName,
        description: category.description,
        pathArray: pathArray,
        id: category._id
      };
    }
  }

  if (updatedBale.relatedCategories?.length > 0) {
    const populated = [];

    for (const id of updatedBale.relatedCategories) {
      const cat = await Category.findById(id);
      if (cat) {
        const pathArray = cat.name.split(' > ');
        const actualCategoryName = pathArray[pathArray.length - 1];
        
        populated.push({
          _id: cat._id,
          name: actualCategoryName,
          description: cat.description,
          pathArray: pathArray,
          id: cat._id
        });
      }
    }

    updatedBale.relatedCategories = populated;
  }

  res.status(200).json({
    status: 'success',
    data: {
      bale: updatedBale
    }
  });
});


/**
 * Delete bale by ID for the logged-in creator
 * @route DELETE /api/v1/creators/bales/:id
 * @access Private (Creator only)
 */
exports.deleteMyBale = catchAsync(async (req, res, next) => {
  // Find bale
  const bale = await Bale.findOne({
    _id: req.params.id,
    creator: req.user.id
  });

  if (!bale) {
    return next(new AppError('No bale found with that ID', 404));
  }

  // Check if bale is in a state that can be deleted
  if (!['draft', 'pending', 'rejected', 'inactive'].includes(bale.status)) {
    return next(new AppError(`Cannot delete bale in ${bale.status} status`, 400));
  }

  // Update creator's bale counts
  if (bale.status === 'pending') {
    await Creator.findByIdAndUpdate(req.user.id, {
      $inc: { 'metrics.pendingBales': -1 }
    });
  } else if (bale.status === 'inactive') {
    await Creator.findByIdAndUpdate(req.user.id, {
      $inc: { 'metrics.inactiveBales': -1 }
    });
  }

  // Delete the bale
  await Bale.findByIdAndDelete(req.params.id);

  res.status(204).json({
    status: 'success',
    data: null
  });
});

/**
 * Get bale statistics for the logged-in creator
 * @route GET /api/v1/creators/bales/stats
 * @access Private (Creator only)
 */
exports.getBaleStats = catchAsync(async (req, res, next) => {
  const stats = await Bale.aggregate([
    {
      $match: { creator: req.user._id }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgPrice: { $avg: '$basePrice' }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);

  const totalBales = await Bale.countDocuments({ creator: req.user.id });
  const draftBales = await Bale.countDocuments({ creator: req.user.id, status: 'draft' });
  const pendingBales = await Bale.countDocuments({ creator: req.user.id, status: 'pending' });
  const activeBales = await Bale.countDocuments({ creator: req.user.id, status: 'active' });
  const inactiveBales = await Bale.countDocuments({ creator: req.user.id, status: 'inactive' });
  const rejectedBales = await Bale.countDocuments({ creator: req.user.id, status: 'rejected' });

  res.status(200).json({
    status: 'success',
    data: {
      stats,
      summary: {
        total: totalBales,
        draft: draftBales,
        pending: pendingBales,
        active: activeBales,
        inactive: inactiveBales,
        rejected: rejectedBales
      }
    }
  });
});
