const mongoose = require('mongoose');
const { Creator } = require('../../models/user.model');
const Product = require('../../models/product.model');
const Bale = require('../../models/bale.model');
const Order = require('../../models/order.model');
const Payout = require('../../models/payout.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');


/**
 * Get creator profile
 * @route GET /api/v1/creators/profile
 * @access Private (Creator only)
 */
exports.getProfile = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.user.id)
    .select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      creator
    }
  });
});

/**
 * Update creator profile
 * @route PATCH /api/v1/creators/profile
 * @access Private (Creator only)
 */
exports.updateProfile = catchAsync(async (req, res, next) => {
  // Create a filtered body to prevent unwanted fields
  const filteredBody = {};
  const allowedFields = ['name', 'email', 'phone', 'gender', 'dateOfBirth'];

  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      filteredBody[key] = req.body[key];
    }
  });

  // Handle profile photo if uploaded
  if (req.file) {
    filteredBody.photo = req.file.path.replace(/\\/g, '/');
  }

  // Update creator profile
  const updatedCreator = await Creator.findByIdAndUpdate(
    req.user.id,
    filteredBody,
    {
      new: true,
      runValidators: true
    }
  ).select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  res.status(200).json({
    status: 'success',
    data: {
      creator: updatedCreator
    }
  });
});

/**
 * Get business information
 * @route GET /api/v1/creators/profile/business-info
 * @access Private (Creator only)
 */
exports.getBusinessInfo = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.user.id)
    .select('businessInfo verificationStatus verificationDetails');

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      businessInfo: creator.businessInfo,
      verificationStatus: creator.verificationStatus,
      verificationDetails: creator.verificationDetails
    }
  });
});

/**
 * Update business information
 * @route PATCH /api/v1/creators/profile/business-info
 * @access Private (Creator only)
 */
exports.updateBusinessInfo = catchAsync(async (req, res, next) => {
  // Get creator
  const creator = await Creator.findById(req.user.id);

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  // Update business information
  if (req.body.businessName) creator.businessInfo.businessName = req.body.businessName;
  if (req.body.businessType) creator.businessInfo.businessType = req.body.businessType;
  if (req.body.ownerName) creator.businessInfo.ownerName = req.body.ownerName;
  if (req.body.ownerID) creator.businessInfo.ownerID = req.body.ownerID;
  if (req.body.taxId) creator.businessInfo.taxId = req.body.taxId;
  if (req.body.phoneNumber) creator.businessInfo.phoneNumber = req.body.phoneNumber;

  // Initialize businessAddress if it doesn't exist
  if (!creator.businessInfo.businessAddress) {
    creator.businessInfo.businessAddress = {
      country: 'Ghana' // Default country
    };
  }

  // Update business address if provided
  if (req.body.businessAddress) {
    // Update specific fields if provided
    if (req.body.businessAddress.addressLine1) {
      creator.businessInfo.businessAddress.addressLine1 = req.body.businessAddress.addressLine1;
    }
    if (req.body.businessAddress.addressLine2) {
      creator.businessInfo.businessAddress.addressLine2 = req.body.businessAddress.addressLine2;
    }
    if (req.body.businessAddress.city) {
      creator.businessInfo.businessAddress.city = req.body.businessAddress.city;
    }
    if (req.body.businessAddress.state) {
      creator.businessInfo.businessAddress.state = req.body.businessAddress.state;
    }
    if (req.body.businessAddress.country) {
      creator.businessInfo.businessAddress.country = req.body.businessAddress.country;
    }
    if (req.body.businessAddress.digitalGps) {
      creator.businessInfo.businessAddress.digitalGps = req.body.businessAddress.digitalGps;
    }
  }

  // Update verification documents if provided
  if (req.body.verificationDocuments && Array.isArray(req.body.verificationDocuments)) {
    creator.businessInfo.verificationDocuments = req.body.verificationDocuments;
    creator.verificationDetails = {
      ...creator.verificationDetails,
      submittedAt: Date.now(),
      documents: req.body.verificationDocuments
    };
    creator.verificationStatus = 'pending';
  }

  // Save the updated creator
  await creator.save();

  res.status(200).json({
    status: 'success',
    data: {
      businessInfo: creator.businessInfo,
      verificationStatus: creator.verificationStatus
    }
  });
});

/**
 * Get payment information
 * @route GET /api/v1/creators/profile/payment-info
 * @access Private (Creator only)
 */
exports.getPaymentInfo = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.user.id)
    .select('paymentInfo payoutPreferences');

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      paymentInfo: creator.paymentInfo,
      payoutPreferences: creator.payoutPreferences
    }
  });
});

/**
 * Update payment information
 * @route PATCH /api/v1/creators/profile/payment-info
 * @access Private (Creator only)
 */
exports.updatePaymentInfo = catchAsync(async (req, res, next) => {
  // Check if request body exists
  if (!req.body || Object.keys(req.body).length === 0) {
    return next(new AppError('Please provide payment information to update', 400));
  }

  // Validate payment option
  if (req.body.paymentOption && !['bank', 'mobile_money'].includes(req.body.paymentOption)) {
    return next(new AppError('Please provide a valid payment option (bank or mobile_money)', 400));
  }

  // Get creator
  const creator = await Creator.findById(req.user.id);

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  // Initialize paymentInfo if it doesn't exist
  if (!creator.paymentInfo) {
    creator.paymentInfo = {};
  }

  // Update payment information
  if (req.body.paymentOption) {
    creator.paymentInfo.paymentOption = req.body.paymentOption;
  }

  // Update bank details if provided
  if (req.body.bankDetails) {
    // Initialize bankDetails if it doesn't exist
    if (!creator.paymentInfo.bankDetails) {
      creator.paymentInfo.bankDetails = {};
    }

    // Update specific bank details fields if provided
    if (req.body.bankDetails.accountNumber) {
      creator.paymentInfo.bankDetails.accountNumber = req.body.bankDetails.accountNumber;
    }
    if (req.body.bankDetails.accountName) {
      creator.paymentInfo.bankDetails.accountName = req.body.bankDetails.accountName;
    }
    if (req.body.bankDetails.bankName) {
      creator.paymentInfo.bankDetails.bankName = req.body.bankDetails.bankName;
    }
    if (req.body.bankDetails.branchName) {
      creator.paymentInfo.bankDetails.branchName = req.body.bankDetails.branchName;
    }
    if (req.body.bankDetails.swiftCode) {
      creator.paymentInfo.bankDetails.swiftCode = req.body.bankDetails.swiftCode;
    }
  }

  // Update mobile money details if provided
  if (req.body.mobileMoneyDetails) {
    // Initialize mobileMoneyDetails if it doesn't exist
    if (!creator.paymentInfo.mobileMoneyDetails) {
      creator.paymentInfo.mobileMoneyDetails = {};
    }

    // Update specific mobile money details fields if provided
    if (req.body.mobileMoneyDetails.serviceProvider) {
      creator.paymentInfo.mobileMoneyDetails.serviceProvider = req.body.mobileMoneyDetails.serviceProvider;
    }
    if (req.body.mobileMoneyDetails.registeredNumber) {
      creator.paymentInfo.mobileMoneyDetails.registeredNumber = req.body.mobileMoneyDetails.registeredNumber;
    }
    if (req.body.mobileMoneyDetails.registeredName) {
      creator.paymentInfo.mobileMoneyDetails.registeredName = req.body.mobileMoneyDetails.registeredName;
    }
  }

  // Update payout preferences if provided
  if (req.body.payoutPreferences) {
    // Initialize payoutPreferences if it doesn't exist
    if (!creator.payoutPreferences) {
      creator.payoutPreferences = {};
    }

    // Update specific payout preferences fields if provided
    if (req.body.payoutPreferences.frequency) {
      creator.payoutPreferences.frequency = req.body.payoutPreferences.frequency;
    }
    if (req.body.payoutPreferences.minimumAmount) {
      creator.payoutPreferences.minimumAmount = req.body.payoutPreferences.minimumAmount;
    }
    if (req.body.payoutPreferences.automaticPayouts !== undefined) {
      creator.payoutPreferences.automaticPayouts = req.body.payoutPreferences.automaticPayouts;
    }
  }

  // Save the updated creator
  await creator.save();

  res.status(200).json({
    status: 'success',
    data: {
      paymentInfo: creator.paymentInfo,
      payoutPreferences: creator.payoutPreferences
    }
  });
});

/**
 * Get shop information
 * @route GET /api/v1/creators/profile/shop-info
 * @access Private (Creator only)
 */
exports.getShopInfo = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.user.id)
    .select('shopInfo socialMedia');

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      shopInfo: creator.shopInfo,
      socialMedia: creator.socialMedia
    }
  });
});

/**
 * Update shop information
 * @route PATCH /api/v1/creators/profile/shop-info
 * @access Private (Creator only)
 */
exports.updateShopInfo = catchAsync(async (req, res, next) => {
  // Check if request body exists
  if (!req.body || Object.keys(req.body).length === 0) {
    return next(new AppError('Please provide shop information to update', 400));
  }



  // Process form data for nested objects
  // Handle contact fields
  if (req.body['contact[name]'] || req.body['contact[email]'] || req.body['contact[phone]']) {
    req.body.contact = {
      name: req.body['contact[name]'],
      email: req.body['contact[email]'],
      phone: req.body['contact[phone]']
    };
    // Delete the original form fields
    delete req.body['contact[name]'];
    delete req.body['contact[email]'];
    delete req.body['contact[phone]'];
  }



  // Handle social media fields
  if (req.body['socialMedia[instagram]'] || req.body['socialMedia[facebook]'] || req.body['socialMedia[twitter]'] ||
      req.body['socialMedia[tiktok]'] || req.body['socialMedia[website]']) {
    req.body.socialMedia = {
      instagram: req.body['socialMedia[instagram]'],
      facebook: req.body['socialMedia[facebook]'],
      twitter: req.body['socialMedia[twitter]'],
      tiktok: req.body['socialMedia[tiktok]'],
      website: req.body['socialMedia[website]']
    };
    // Delete the original form fields
    delete req.body['socialMedia[instagram]'];
    delete req.body['socialMedia[facebook]'];
    delete req.body['socialMedia[twitter]'];
    delete req.body['socialMedia[tiktok]'];
    delete req.body['socialMedia[website]'];
  }



  // Handle customer care fields
  if (req.body['customerCare[name]'] || req.body['customerCare[email]'] || req.body['customerCare[phone]'] ||
      req.body['customerCare[addressLine1]'] || req.body['customerCare[addressLine2]'] || req.body['customerCare[city]'] ||
      req.body['customerCare[region]'] || req.body['customerCare[country]'] || req.body['customerCare[hours]'] ||
      req.body['customerCare[supportWebsite]']) {
    req.body.customerCare = {
      name: req.body['customerCare[name]'],
      email: req.body['customerCare[email]'],
      phone: req.body['customerCare[phone]'],
      addressLine1: req.body['customerCare[addressLine1]'],
      addressLine2: req.body['customerCare[addressLine2]'],
      city: req.body['customerCare[city]'],
      region: req.body['customerCare[region]'],
      country: req.body['customerCare[country]'],
      hours: req.body['customerCare[hours]'],
      supportWebsite: req.body['customerCare[supportWebsite]']
    };
    // Delete the original form fields
    delete req.body['customerCare[name]'];
    delete req.body['customerCare[email]'];
    delete req.body['customerCare[phone]'];
    delete req.body['customerCare[addressLine1]'];
    delete req.body['customerCare[addressLine2]'];
    delete req.body['customerCare[city]'];
    delete req.body['customerCare[region]'];
    delete req.body['customerCare[country]'];
    delete req.body['customerCare[hours]'];
    delete req.body['customerCare[supportWebsite]'];
  }




  // Validate required fields
  const requiredFields = ['name'];
  for (const field of requiredFields) {
    if (req.body[field] === undefined) {
      return next(new AppError(`Please provide shop ${field}`, 400));
    }
  }

  // Validate contact fields
  if (!req.body.contact || !req.body.contact.name || !req.body.contact.email || !req.body.contact.phone) {
    return next(new AppError('Please provide contact name, email, and phone', 400));
  }

  // Get creator
  const creator = await Creator.findById(req.user.id);

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  // Initialize shopInfo if it doesn't exist
  if (!creator.shopInfo) {
    creator.shopInfo = {};
  }

  // Update shop information
  creator.shopInfo.name = req.body.name;
  creator.shopInfo.contact = req.body.contact;

  // Update optional shop fields if provided
  const optionalFields = ['logo', 'banner', 'description', 'customerCare'];
  optionalFields.forEach(field => {
    if (req.body[field]) {
      creator.shopInfo[field] = req.body[field];
    }
  });

  // Update social media if provided
  if (req.body.socialMedia) {
    // Initialize socialMedia if it doesn't exist
    if (!creator.socialMedia) {
      creator.socialMedia = {};
    }

    // Update specific social media fields if provided
    if (req.body.socialMedia.instagram) {
      creator.socialMedia.instagram = req.body.socialMedia.instagram;
    }
    if (req.body.socialMedia.facebook) {
      creator.socialMedia.facebook = req.body.socialMedia.facebook;
    }
    if (req.body.socialMedia.twitter) {
      creator.socialMedia.twitter = req.body.socialMedia.twitter;
    }
    if (req.body.socialMedia.tiktok) {
      creator.socialMedia.tiktok = req.body.socialMedia.tiktok;
    }
    if (req.body.socialMedia.website) {
      creator.socialMedia.website = req.body.socialMedia.website;
    }
  }



  // Save the updated creator
  await creator.save();

  res.status(200).json({
    status: 'success',
    data: {
      shopInfo: creator.shopInfo,
      socialMedia: creator.socialMedia
    }
  });
});

/**
 * Get shipping information
 * @route GET /api/v1/creators/profile/shipping-info
 * @access Private (Creator only)
 */
exports.getShippingInfo = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.user.id)
    .select('shippingInfo');

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      shippingInfo: creator.shippingInfo
    }
  });
});

/**
 * Update shipping information
 * @route PATCH /api/v1/creators/profile/shipping-info
 * @access Private (Creator only)
 */
exports.updateShippingInfo = catchAsync(async (req, res, next) => {

  // Check if request body exists
  if (!req.body || Object.keys(req.body).length === 0) {
    console.log('Request body is empty, returning error');
    return next(new AppError('Please provide shipping information to update', 400));
  }

  // Get creator
  const creator = await Creator.findById(req.user.id);

  if (!creator) {
    return next(new AppError('Creator not found', 404));
  }

  // Initialize shippingInfo if it doesn't exist
  if (!creator.shippingInfo) {
    creator.shippingInfo = {};
  }

  // Normalize the request body structure
  // If the data is not nested under shippingInfo, restructure it
  const shippingData = req.body.shippingInfo || req.body;


  // Update shipping address if provided
  if (shippingData.shippingAddress) {
    // Initialize shippingAddress if it doesn't exist
    if (!creator.shippingInfo.shippingAddress) {
      creator.shippingInfo.shippingAddress = {
        country: 'Ghana' // Default country
      };
    }

    // Update specific shipping address fields if provided
    if (shippingData.shippingAddress.addressLine1) {
      creator.shippingInfo.shippingAddress.addressLine1 = shippingData.shippingAddress.addressLine1;
    }
    if (shippingData.shippingAddress.addressLine2) {
      creator.shippingInfo.shippingAddress.addressLine2 = shippingData.shippingAddress.addressLine2;
    }
    if (shippingData.shippingAddress.city) {
      creator.shippingInfo.shippingAddress.city = shippingData.shippingAddress.city;
    }
    if (shippingData.shippingAddress.state) {
      creator.shippingInfo.shippingAddress.state = shippingData.shippingAddress.state;
    }
    if (shippingData.shippingAddress.zone) {
      creator.shippingInfo.shippingAddress.zone = shippingData.shippingAddress.zone;
    }
    if (shippingData.shippingAddress.country) {
      creator.shippingInfo.shippingAddress.country = shippingData.shippingAddress.country;
    }
    if (shippingData.shippingAddress.postalCode) {
      creator.shippingInfo.shippingAddress.postalCode = shippingData.shippingAddress.postalCode;
    }
    if (shippingData.shippingAddress.digitalGps) {
      creator.shippingInfo.shippingAddress.digitalGps = shippingData.shippingAddress.digitalGps;
      // Also update gpsAddress for backward compatibility
      creator.shippingInfo.shippingAddress.gpsAddress = shippingData.shippingAddress.digitalGps;
    } else if (shippingData.shippingAddress.gpsAddress) {
      creator.shippingInfo.shippingAddress.gpsAddress = shippingData.shippingAddress.gpsAddress;
      // Also update digitalGps for consistency
      creator.shippingInfo.shippingAddress.digitalGps = shippingData.shippingAddress.gpsAddress;
    }
    if (shippingData.shippingAddress.phone) {
      creator.shippingInfo.shippingAddress.phone = shippingData.shippingAddress.phone;
    }
  }

  // Update return address if provided
  if (shippingData.returnAddress) {
    // Initialize returnAddress if it doesn't exist
    if (!creator.shippingInfo.returnAddress) {
      creator.shippingInfo.returnAddress = {};
    }

    // Check if using same address as shipping
    if (shippingData.returnAddress.useSameAddress) {
      creator.shippingInfo.returnAddress = {
        ...creator.shippingInfo.shippingAddress,
        useSameAddress: true
      };
    } else {
      // Update specific return address fields if provided
      if (shippingData.returnAddress.addressLine1) {
        creator.shippingInfo.returnAddress.addressLine1 = shippingData.returnAddress.addressLine1;
      }
      if (shippingData.returnAddress.addressLine2) {
        creator.shippingInfo.returnAddress.addressLine2 = shippingData.returnAddress.addressLine2;
      }
      if (shippingData.returnAddress.city) {
        creator.shippingInfo.returnAddress.city = shippingData.returnAddress.city;
      }
      if (shippingData.returnAddress.state) {
        creator.shippingInfo.returnAddress.state = shippingData.returnAddress.state;
      }
      if (shippingData.returnAddress.zone) {
        creator.shippingInfo.returnAddress.zone = shippingData.returnAddress.zone;
      }
      if (shippingData.returnAddress.country) {
        creator.shippingInfo.returnAddress.country = shippingData.returnAddress.country;
      } else {
        // Default country
        creator.shippingInfo.returnAddress.country = 'Ghana';
      }
      if (shippingData.returnAddress.postalCode) {
        creator.shippingInfo.returnAddress.postalCode = shippingData.returnAddress.postalCode;
      }
      if (shippingData.returnAddress.digitalGps) {
        creator.shippingInfo.returnAddress.digitalGps = shippingData.returnAddress.digitalGps;
        // Also update gpsAddress for backward compatibility
        creator.shippingInfo.returnAddress.gpsAddress = shippingData.returnAddress.digitalGps;
      } else if (shippingData.returnAddress.gpsAddress) {
        creator.shippingInfo.returnAddress.gpsAddress = shippingData.returnAddress.gpsAddress;
        // Also update digitalGps for consistency
        creator.shippingInfo.returnAddress.digitalGps = shippingData.returnAddress.gpsAddress;
      }
      if (shippingData.returnAddress.phone) {
        creator.shippingInfo.returnAddress.phone = shippingData.returnAddress.phone;
      }
      creator.shippingInfo.returnAddress.useSameAddress = false;
    }
  }

  // Update shipping policy if provided
  if (shippingData.policy) {
    creator.shippingInfo.policy = shippingData.policy;
  }

  // Update shipping methods if provided
  if (shippingData.methods) {
    // Handle both array and string formats (for form data)
    if (typeof shippingData.methods === 'string') {
      try {
        creator.shippingInfo.methods = JSON.parse(shippingData.methods);
      } catch (err) {
        console.log('Error parsing shipping methods:', err); 
      }
    } else if (Array.isArray(shippingData.methods)) {
      creator.shippingInfo.methods = shippingData.methods;
    }
  }

  // Save the updated creator
  await creator.save();

  res.status(200).json({
    status: 'success',
    data: {
      shippingInfo: creator.shippingInfo
    }
  });
});

/**
 * Get creator dashboard statistics
 * @route GET /api/v1/creators/dashboard
 * @access Private (Creator only)
 */
/**
 * Update profile photo
 * @route PATCH /api/v1/creators/profile/photo
 * @access Private (Creator only)
 */
exports.updateProfilePhoto = catchAsync(async (req, res, next) => {
  // Check if file was uploaded
  if (!req.file) {
    return next(new AppError('Please upload a profile photo', 400));
  }

  // Convert backslashes to forward slashes for consistency
  const photoPath = req.file.path.replace(/\\/g, '/');

  // Update creator profile with new photo
  const updatedCreator = await Creator.findByIdAndUpdate(
    req.user.id,
    { photo: photoPath },
    {
      new: true,
      runValidators: true
    }
  ).select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  res.status(200).json({
    status: 'success',
    data: {
      creator: updatedCreator
    }
  });
});

/**
 * Get creator dashboard statistics
 * @route GET /api/v1/creators/profile/dashboard
 * @access Private (Creator only)
 */
exports.getDashboardStats = catchAsync(async (req, res, next) => {
  // Get creator metrics
  const creator = await Creator.findById(req.user.id).select('metrics');

  // Get recent products
  const recentProducts = await Product.find({ creator: req.user.id })
    .sort('-createdAt')
    .limit(5)
    .select('name basePrice images status createdAt');

  // Get recent bales
  const recentBales = await Bale.find({ creator: req.user.id })
    .sort('-createdAt')
    .limit(5)
    .select('name basePrice images status createdAt');

  // Get recent orders
  const recentOrders = await Order.aggregate([
    {
      $match: {
        'items.creator': new mongoose.Types.ObjectId(req.user.id)
      }
    },
    {
      $addFields: {
        creatorItems: {
          $filter: {
            input: '$items',
            as: 'item',
            cond: { $eq: ['$$item.creator', new mongoose.Types.ObjectId(req.user.id)] }
          }
        }
      }
    },
    {
      $project: {
        _id: 1,
        user: 1,
        creatorItems: 1,
        status: 1,
        total: 1,
        isPaid: 1,
        createdAt: 1,
        creatorTotal: {
          $sum: {
            $map: {
              input: '$creatorItems',
              as: 'item',
              in: { $multiply: ['$$item.price', '$$item.quantity'] }
            }
          }
        }
      }
    },
    {
      $sort: { createdAt: -1 }
    },
    {
      $limit: 5
    }
  ]);

  // Get recent payouts
  const recentPayouts = await Payout.find({ creator: req.user.id })
    .sort('-createdAt')
    .limit(5)
    .select('amount status createdAt');

  res.status(200).json({
    status: 'success',
    data: {
      metrics: creator.metrics,
      recentProducts,
      recentBales,
      recentOrders,
      recentPayouts
    }
  });
});
