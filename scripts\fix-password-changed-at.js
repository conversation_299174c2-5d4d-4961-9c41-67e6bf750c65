const mongoose = require('mongoose');
const { BaseUser } = require('../models/user.model');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.DATABASE_URI || 'mongodb://localhost:27017/everyfash');
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Fix users with problematic passwordChangedAt values
const fixPasswordChangedAt = async () => {
  try {
    console.log('Starting password change fix...');
    
    // Find all users with passwordChangedAt set
    const usersWithPasswordChangedAt = await BaseUser.find({
      passwordChangedAt: { $exists: true, $ne: null }
    }).select('_id email passwordChangedAt createdAt');
    
    console.log(`Found ${usersWithPasswordChangedAt.length} users with passwordChangedAt set`);
    
    let fixedCount = 0;
    
    for (const user of usersWithPasswordChangedAt) {
      // Check if passwordChangedAt is very close to createdAt (indicating it was set during registration)
      const timeDiff = Math.abs(user.passwordChangedAt.getTime() - user.createdAt.getTime());
      const oneMinute = 60 * 1000; // 1 minute in milliseconds
      
      if (timeDiff < oneMinute) {
        console.log(`Fixing user ${user.email} - passwordChangedAt was set during registration`);
        
        // Remove passwordChangedAt for users where it was set during registration
        await BaseUser.updateOne(
          { _id: user._id },
          { $unset: { passwordChangedAt: 1 } }
        );
        
        fixedCount++;
      } else {
        console.log(`User ${user.email} - passwordChangedAt appears to be legitimate (${timeDiff}ms after creation)`);
      }
    }
    
    console.log(`Fixed ${fixedCount} users`);
    
    // Also check for any users with passwordChangedAt in the future
    const now = new Date();
    const usersWithFuturePasswordChange = await BaseUser.find({
      passwordChangedAt: { $gt: now }
    }).select('_id email passwordChangedAt');
    
    if (usersWithFuturePasswordChange.length > 0) {
      console.log(`Found ${usersWithFuturePasswordChange.length} users with passwordChangedAt in the future`);
      
      for (const user of usersWithFuturePasswordChange) {
        console.log(`Fixing user ${user.email} - passwordChangedAt is in the future`);
        
        await BaseUser.updateOne(
          { _id: user._id },
          { $unset: { passwordChangedAt: 1 } }
        );
        
        fixedCount++;
      }
    }
    
    console.log(`Total users fixed: ${fixedCount}`);
    
  } catch (error) {
    console.error('Error fixing passwordChangedAt:', error);
  }
};

// Main function
const main = async () => {
  await connectDB();
  await fixPasswordChangedAt();
  await mongoose.disconnect();
  console.log('Migration completed');
};

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { fixPasswordChangedAt };
