const Product = require('../../models/product.model');
const Bale = require('../../models/bale.model');
const Order = require('../../models/order.model');
const { Creator, Buyer } = require('../../models/user.model');
const Review = require('../../models/review.model');
const Payout = require('../../models/payout.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get dashboard statistics
 * @route GET /api/v1/admin/dashboard
 * @access Private (Admin only)
 */
exports.getDashboardStats = catchAsync(async (req, res, next) => {
  // Get time period from query params (default to 30 days)
  const period = req.query.period || '30';
  
  // Calculate date ranges
  const now = new Date();
  let startDate;
  
  switch (period) {
    case '7':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '90':
      startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case 'all':
      startDate = new Date(0); // Beginning of time
      break;
    default: // 30 days
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  }

  // Get user statistics
  const totalCreators = await Creator.countDocuments();
  const verifiedCreators = await Creator.countDocuments({ verificationStatus: 'verified' });
  const pendingCreators = await Creator.countDocuments({ verificationStatus: 'pending' });
  const totalBuyers = await Buyer.countDocuments();
  const newCreators = await Creator.countDocuments({ createdAt: { $gte: startDate } });
  const newBuyers = await Buyer.countDocuments({ createdAt: { $gte: startDate } });

  // Get product statistics
  const totalProducts = await Product.countDocuments();
  const activeProducts = await Product.countDocuments({ status: 'active' });
  const pendingProducts = await Product.countDocuments({ status: 'pending' });
  const totalBales = await Bale.countDocuments();
  const activeBales = await Bale.countDocuments({ status: 'active' });
  const pendingBales = await Bale.countDocuments({ status: 'pending' });

  // Get order statistics
  const totalOrders = await Order.countDocuments();
  const pendingOrders = await Order.countDocuments({ status: 'pending' });
  const processingOrders = await Order.countDocuments({ status: 'processing' });
  const shippedOrders = await Order.countDocuments({ status: 'shipped' });
  const deliveredOrders = await Order.countDocuments({ status: 'delivered' });
  const cancelledOrders = await Order.countDocuments({ status: 'cancelled' });
  const newOrders = await Order.countDocuments({ createdAt: { $gte: startDate } });

  // Get revenue statistics
  const revenueStats = await Order.aggregate([
    {
      $match: {
        status: 'delivered',
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$total' },
        avgOrderValue: { $avg: '$total' },
        orderCount: { $sum: 1 }
      }
    }
  ]);

  // Get revenue by day
  const revenueByDay = await Order.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
        revenue: { $sum: '$total' },
        orderCount: { $sum: 1 }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);

  // Get payout statistics
  const totalPayouts = await Payout.countDocuments();
  const pendingPayouts = await Payout.countDocuments({ status: 'pending' });
  const completedPayouts = await Payout.countDocuments({ status: 'completed' });
  const payoutAmount = await Payout.aggregate([
    {
      $match: {
        status: 'completed'
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$amount' }
      }
    }
  ]);

  // Get top creators
  const topCreators = await Creator.find({ verificationStatus: 'verified' })
    .sort('-metrics.totalSales')
    .limit(5)
    .select('name photo shopInfo metrics');

  // Get top products
  const topProducts = await Product.find({ status: 'active' })
    .sort('-sold')
    .limit(5)
    .select('name images basePrice sold ratingsAverage');

  // Get top bales
  const topBales = await Bale.find({ status: 'active' })
    .sort('-sold')
    .limit(5)
    .select('name images basePrice sold ratingsAverage');

  res.status(200).json({
    status: 'success',
    data: {
      users: {
        creators: {
          total: totalCreators,
          verified: verifiedCreators,
          pending: pendingCreators,
          new: newCreators
        },
        buyers: {
          total: totalBuyers,
          new: newBuyers
        }
      },
      products: {
        total: totalProducts,
        active: activeProducts,
        pending: pendingProducts
      },
      bales: {
        total: totalBales,
        active: activeBales,
        pending: pendingBales
      },
      orders: {
        total: totalOrders,
        pending: pendingOrders,
        processing: processingOrders,
        shipped: shippedOrders,
        delivered: deliveredOrders,
        cancelled: cancelledOrders,
        new: newOrders
      },
      revenue: {
        total: revenueStats.length > 0 ? revenueStats[0].totalRevenue : 0,
        avgOrderValue: revenueStats.length > 0 ? revenueStats[0].avgOrderValue : 0,
        byDay: revenueByDay
      },
      payouts: {
        total: totalPayouts,
        pending: pendingPayouts,
        completed: completedPayouts,
        amount: payoutAmount.length > 0 ? payoutAmount[0].total : 0
      },
      top: {
        creators: topCreators,
        products: topProducts,
        bales: topBales
      },
      period: {
        days: period === 'all' ? 'all time' : period,
        startDate,
        endDate: now
      }
    }
  });
});
