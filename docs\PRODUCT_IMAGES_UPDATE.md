# Product Images Update Endpoint

## Overview
This endpoint allows updating product images by sending the desired final state. The frontend sends which existing images to keep plus any new files to upload, and the backend handles the rest automatically.

## Endpoint Details

### Route
```
PATCH /api/v1/creators/products/:id/images
```

### Authentication
- **Required**: Bearer token (Creator only)
- **Access**: Private - Only the product owner can update images

## How It Works

The frontend sends:
1. **Existing images to keep** (via `images` array)
2. **New files to upload** (via `productImages` form field)

The backend automatically:
1. **Validates** existing images are actually current product images
2. **Uploads** new files to Cloudinary
3. **Combines** kept images + uploaded images
4. **Removes** old images not in the final list (via cleanup middleware)
5. **Updates** the product with the final image array

## Request Formats

### 1. Keep Some Existing + Upload New (Most Common)

**Option A: Form Array (Recommended)**
```javascript
// Form Data
{
  "images[]": "url1.jpg",     // First existing image to keep
  "images[]": "url2.jpg",     // Second existing image to keep
  "productImages": [File, File, ...] // New files to upload
}
```

**Option B: JSON String**
```javascript
// Form Data
{
  "images": "[\"url1.jpg\", \"url2.jpg\"]", // JSON string of existing images to keep
  "productImages": [File, File, ...]        // New files to upload
}
```

### 2. Upload Only (Replace All)
```javascript
// Form Data
{
  "productImages": [File, File, ...] // New files only, removes all existing
}
```

### 3. Keep Existing Only (Remove Some)
```javascript
// JSON Body
{
  "images": [
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/keep1.jpg",
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/keep2.jpg"
  ]
}
```

### 4. Reorder Existing Images
```javascript
// JSON Body - Same images, different order
{
  "images": [
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image3.jpg", // Now first
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image1.jpg", // Now second
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image2.jpg"  // Now third
  ]
}
```

## Response Format

### Success Response (200)
```javascript
{
  "status": "success",
  "message": "Product images updated successfully",
  "data": {
    "product": {
      "_id": "product_id",
      "name": "Product Name",
      "images": [
        "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/kept-image1.jpg",
        "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/new-uploaded-image.jpg"
      ],
      // ... other product fields
    },
    "summary": {
      "totalImages": 2,        // Final number of images
      "addedImages": 1,        // Number of new images uploaded
      "removedImages": 2,      // Number of images removed
      "uploadedNewImages": [   // URLs of newly uploaded images
        "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/new-uploaded-image.jpg"
      ]
    }
  }
}
```

### Error Responses

**400 - No Images Provided**
```javascript
{
  "status": "fail",
  "message": "Product must have at least one image"
}
```

**400 - Invalid Images Format**
```javascript
{
  "status": "fail",
  "message": "Images must be an array"
}
```

**400 - Invalid Existing Images**
```javascript
{
  "status": "fail",
  "message": "Some provided images are not current product images"
}
```

**400 - Too Many Images**
```javascript
{
  "status": "fail",
  "message": "Maximum 10 images allowed per product"
}
```

**404 - Product Not Found**
```javascript
{
  "status": "fail",
  "message": "No product found with that ID"
}
```

## Features

### Automatic Image Cleanup
- **Old Image Removal**: When images are updated, the old images are automatically removed from Cloudinary
- **Middleware Integration**: Uses the existing `createImageCleanupMiddleware` for seamless cleanup
- **Error Handling**: Cleanup failures don't affect the main update operation

### Validation
- **Minimum Images**: At least 1 image required
- **Maximum Images**: Maximum 10 images allowed
- **Array Validation**: Images field must be an array when provided
- **Owner Verification**: Only the product creator can update images

### Upload Integration
- **Cloudinary Storage**: Uses existing Cloudinary configuration
- **File Processing**: Integrates with `uploadProductImages` and `processCloudinaryFiles` middleware
- **Error Handling**: Includes `handleUploadError` middleware for upload failures

## Usage Examples

### Using Postman

1. **File Upload**:
   - Method: PATCH
   - URL: `{{base_url}}/creators/products/{{product_id}}/images`
   - Body: form-data
   - Key: `productImages` (File type)
   - Select multiple image files

2. **Direct URLs**:
   - Method: PATCH
   - URL: `{{base_url}}/creators/products/{{product_id}}/images`
   - Body: raw JSON
   - Content: `{"images": ["url1", "url2"]}`

### Using JavaScript/Fetch

```javascript
// Most common: Keep some existing + upload new (Form Array Method)
const formData = new FormData();
// Add existing images to keep using array notation
formData.append('images[]', 'https://res.cloudinary.com/your-cloud/existing1.jpg');
formData.append('images[]', 'https://res.cloudinary.com/your-cloud/existing2.jpg');
// Add new files to upload
formData.append('productImages', newFile1);
formData.append('productImages', newFile2);

const response = await fetch('/api/v1/creators/products/123/images', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

// Alternative: JSON String Method
const formData = new FormData();
formData.append('images', JSON.stringify([
  'https://res.cloudinary.com/your-cloud/existing1.jpg',
  'https://res.cloudinary.com/your-cloud/existing2.jpg'
]));
formData.append('productImages', newFile1);
formData.append('productImages', newFile2);

// Upload only (replace all existing)
const formData = new FormData();
formData.append('productImages', file1);
formData.append('productImages', file2);

const response = await fetch('/api/v1/creators/products/123/images', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

// Keep only specific existing images (remove others)
const response = await fetch('/api/v1/creators/products/123/images', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    images: [
      'https://res.cloudinary.com/your-cloud/keep1.jpg',
      'https://res.cloudinary.com/your-cloud/keep2.jpg'
    ]
  })
});
```

### Using cURL

```bash
# Keep some existing + upload new (Form Array Method - RECOMMENDED)
curl --location --request PATCH 'https://your-api.com/api/v1/creators/products/123/images' \
--header 'Authorization: Bearer YOUR_TOKEN' \
--form 'images[]="https://res.cloudinary.com/your-cloud/existing1.jpg"' \
--form 'images[]="https://res.cloudinary.com/your-cloud/existing2.jpg"' \
--form 'productImages=@"/path/to/new-image1.jpg"' \
--form 'productImages=@"/path/to/new-image2.jpg"'

# Upload only (replace all)
curl --location --request PATCH 'https://your-api.com/api/v1/creators/products/123/images' \
--header 'Authorization: Bearer YOUR_TOKEN' \
--form 'productImages=@"/path/to/image1.jpg"' \
--form 'productImages=@"/path/to/image2.jpg"'

# Keep existing only (JSON method)
curl --location --request PATCH 'https://your-api.com/api/v1/creators/products/123/images' \
--header 'Authorization: Bearer YOUR_TOKEN' \
--header 'Content-Type: application/json' \
--data '{
  "images": [
    "https://res.cloudinary.com/your-cloud/keep1.jpg",
    "https://res.cloudinary.com/your-cloud/keep2.jpg"
  ]
}'
```

## Testing

### Postman Collection
- **File**: `postman/product-images-update.json`
- **Tests**: Includes success cases, error cases, and edge cases
- **Variables**: Uses `{{creator_token}}` and `{{product_id}}`

### Test Script
- **File**: `test-update-images.js`
- **Coverage**: Success scenarios, error validation, limits testing
- **Usage**: Update token and product ID, then run `node test-update-images.js`

## Integration Notes

### Middleware Stack
```javascript
router.patch('/:id/images',
  uploadMiddleware.uploadProductImages,      // Handle file uploads
  uploadMiddleware.processCloudinaryFiles,   // Process uploaded files
  uploadMiddleware.handleUploadError,        // Handle upload errors
  productController.updateImages             // Update product images
);
```

### Database Impact
- **Field Updated**: `product.images` array
- **Cleanup**: Automatic removal of old images from Cloudinary
- **Validation**: Mongoose schema validation applied

### Performance Considerations
- **Image Limits**: 10 image maximum prevents excessive storage usage
- **Async Cleanup**: Image cleanup runs asynchronously to avoid blocking
- **Error Isolation**: Cleanup failures don't affect the main operation
