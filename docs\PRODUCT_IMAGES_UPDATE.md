# Product Images Update Endpoint

## Overview
This document describes the enhanced endpoint for updating product images with granular operations including add, remove, replace, and reorder functionality.

## Endpoint Details

### Route
```
PATCH /api/v1/creators/products/:id/images
```

### Authentication
- **Required**: Bearer token (Creator only)
- **Access**: Private - Only the product owner can update images

## Operations

The endpoint supports four main operations via the `operation` parameter:

### 1. Replace Operation (Default)
Completely replaces all existing images with new ones.

```javascript
// JSON Body
{
  "operation": "replace", // Optional, default behavior
  "images": [
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image1.jpg",
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image2.jpg"
  ]
}

// Form Data
{
  "operation": "replace",
  "productImages": [File, File, ...] // Max 10 files
}
```

### 2. Add Operation
Adds new images to existing ones without removing current images.

```javascript
// JSON Body
{
  "operation": "add",
  "images": [
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/new-image1.jpg",
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/new-image2.jpg"
  ]
}

// Form Data
{
  "operation": "add",
  "productImages": [File, File, ...] // New files to upload
}
```

### 3. Remove Operation
Removes specific images by URL or index position.

```javascript
// Remove by URL
{
  "operation": "remove",
  "imagesToRemove": [
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/old-image1.jpg",
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/old-image2.jpg"
  ]
}

// Remove by index (0-based)
{
  "operation": "remove",
  "imagesToRemove": [0, 2, 4] // Removes 1st, 3rd, and 5th images
}
```

### 4. Reorder Operation
Changes the order of existing images without adding or removing any.

```javascript
{
  "operation": "reorder",
  "imageOrder": [
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image3.jpg", // Now first
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image1.jpg", // Now second
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image2.jpg"  // Now third
  ]
}
```

## Legacy Support

For backward compatibility, the endpoint still supports the original format without the `operation` parameter:

```javascript
// Legacy format (equivalent to operation: "replace")
{
  "images": ["url1.jpg", "url2.jpg"]
}
```

## Response Format

### Success Response (200)
```javascript
{
  "status": "success",
  "message": "Product images {operation}d successfully", // e.g., "replaced", "added", "removed", "reordered"
  "data": {
    "product": {
      "_id": "product_id",
      "name": "Product Name",
      "images": [
        "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image1.jpg",
        "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image2.jpg"
      ],
      // ... other product fields
    },
    "operation": "replace", // The operation that was performed
    "imagesCount": 2 // Total number of images after operation
  }
}
```

### Error Responses

**400 - Invalid Operation**
```javascript
{
  "status": "fail",
  "message": "Invalid operation. Must be one of: replace, add, remove, reorder"
}
```

**400 - No Images for Replace**
```javascript
{
  "status": "fail",
  "message": "Please provide at least one image for replacement"
}
```

**400 - No Images to Add**
```javascript
{
  "status": "fail",
  "message": "Please provide images to add"
}
```

**400 - No Images to Remove**
```javascript
{
  "status": "fail",
  "message": "Please provide imagesToRemove array with URLs or indices to remove"
}
```

**400 - Invalid Reorder**
```javascript
{
  "status": "fail",
  "message": "Image order must include all current images"
}
```

**400 - Cannot Remove All Images**
```javascript
{
  "status": "fail",
  "message": "Cannot remove all images. At least one image is required"
}
```

**400 - Invalid Images Format**
```javascript
{
  "status": "fail",
  "message": "Images must be an array"
}
```

**400 - Too Many Images**
```javascript
{
  "status": "fail",
  "message": "Maximum 10 images allowed per product"
}
```

**404 - Product Not Found**
```javascript
{
  "status": "fail",
  "message": "No product found with that ID"
}
```

## Features

### Automatic Image Cleanup
- **Old Image Removal**: When images are updated, the old images are automatically removed from Cloudinary
- **Middleware Integration**: Uses the existing `createImageCleanupMiddleware` for seamless cleanup
- **Error Handling**: Cleanup failures don't affect the main update operation

### Validation
- **Minimum Images**: At least 1 image required
- **Maximum Images**: Maximum 10 images allowed
- **Array Validation**: Images field must be an array when provided
- **Owner Verification**: Only the product creator can update images

### Upload Integration
- **Cloudinary Storage**: Uses existing Cloudinary configuration
- **File Processing**: Integrates with `uploadProductImages` and `processCloudinaryFiles` middleware
- **Error Handling**: Includes `handleUploadError` middleware for upload failures

## Usage Examples

### Using Postman

1. **File Upload**:
   - Method: PATCH
   - URL: `{{base_url}}/creators/products/{{product_id}}/images`
   - Body: form-data
   - Key: `productImages` (File type)
   - Select multiple image files

2. **Direct URLs**:
   - Method: PATCH
   - URL: `{{base_url}}/creators/products/{{product_id}}/images`
   - Body: raw JSON
   - Content: `{"images": ["url1", "url2"]}`

### Using JavaScript/Fetch

```javascript
// Replace all images with file upload
const formData = new FormData();
formData.append('operation', 'replace');
formData.append('productImages', file1);
formData.append('productImages', file2);

const response = await fetch('/api/v1/creators/products/123/images', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

// Add new images via URLs
const response = await fetch('/api/v1/creators/products/123/images', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    operation: 'add',
    images: ['new-url1.jpg', 'new-url2.jpg']
  })
});

// Remove specific images
const response = await fetch('/api/v1/creators/products/123/images', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    operation: 'remove',
    imagesToRemove: ['old-url1.jpg', 0] // Mix of URLs and indices
  })
});

// Reorder images
const response = await fetch('/api/v1/creators/products/123/images', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    operation: 'reorder',
    imageOrder: ['url3.jpg', 'url1.jpg', 'url2.jpg']
  })
});

// Legacy support (backward compatibility)
const response = await fetch('/api/v1/creators/products/123/images', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    images: ['url1.jpg', 'url2.jpg'] // Defaults to replace operation
  })
});
```

## Testing

### Postman Collection
- **File**: `postman/product-images-update.json`
- **Tests**: Includes success cases, error cases, and edge cases
- **Variables**: Uses `{{creator_token}}` and `{{product_id}}`

### Test Script
- **File**: `test-update-images.js`
- **Coverage**: Success scenarios, error validation, limits testing
- **Usage**: Update token and product ID, then run `node test-update-images.js`

## Integration Notes

### Middleware Stack
```javascript
router.patch('/:id/images',
  uploadMiddleware.uploadProductImages,      // Handle file uploads
  uploadMiddleware.processCloudinaryFiles,   // Process uploaded files
  uploadMiddleware.handleUploadError,        // Handle upload errors
  productController.updateImages             // Update product images
);
```

### Database Impact
- **Field Updated**: `product.images` array
- **Cleanup**: Automatic removal of old images from Cloudinary
- **Validation**: Mongoose schema validation applied

### Performance Considerations
- **Image Limits**: 10 image maximum prevents excessive storage usage
- **Async Cleanup**: Image cleanup runs asynchronously to avoid blocking
- **Error Isolation**: Cleanup failures don't affect the main operation

## Related Endpoints
- `PATCH /api/v1/creators/products/:id/basic-info` - Update basic product info
- `PATCH /api/v1/creators/products/:id/specifications` - Update specifications
- `PATCH /api/v1/creators/products/:id/seo` - Update SEO information
- `POST /api/v1/creators/products/:id/variations` - Add product variations (with images)
