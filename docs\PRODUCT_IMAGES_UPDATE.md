# Product Images Update Endpoint

## Overview
This document describes the new endpoint for updating product images, following the granular update pattern established for other product fields.

## Endpoint Details

### Route
```
PATCH /api/v1/creators/products/:id/images
```

### Authentication
- **Required**: Bearer token (Creator only)
- **Access**: Private - Only the product owner can update images

### Request Methods

#### Method 1: File Upload (Form Data)
Upload new image files directly to Cloudinary:

```javascript
// Form Data
{
  "productImages": [File, File, ...] // Max 10 files
}
```

#### Method 2: Direct URLs (JSON)
Provide image URLs directly:

```javascript
// JSON Body
{
  "images": [
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image1.jpg",
    "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image2.jpg"
  ]
}
```

#### Method 3: Mixed (Form Data + URLs)
Combine file uploads with existing URLs:

```javascript
// Form Data
{
  "productImages": [File, File], // New files to upload
  "images": "[\"https://existing-url.jpg\"]" // JSON string of existing URLs
}
```

### Response Format

#### Success Response (200)
```javascript
{
  "status": "success",
  "message": "Product images updated successfully",
  "data": {
    "product": {
      "_id": "product_id",
      "name": "Product Name",
      "images": [
        "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/new-image1.jpg",
        "https://res.cloudinary.com/your-cloud/image/upload/v1234567890/new-image2.jpg"
      ],
      // ... other product fields
    }
  }
}
```

#### Error Responses

**400 - No Images Provided**
```javascript
{
  "status": "fail",
  "message": "Please provide at least one image"
}
```

**400 - Invalid Images Format**
```javascript
{
  "status": "fail",
  "message": "Images must be an array"
}
```

**400 - Too Many Images**
```javascript
{
  "status": "fail",
  "message": "Maximum 10 images allowed per product"
}
```

**404 - Product Not Found**
```javascript
{
  "status": "fail",
  "message": "No product found with that ID"
}
```

## Features

### Automatic Image Cleanup
- **Old Image Removal**: When images are updated, the old images are automatically removed from Cloudinary
- **Middleware Integration**: Uses the existing `createImageCleanupMiddleware` for seamless cleanup
- **Error Handling**: Cleanup failures don't affect the main update operation

### Validation
- **Minimum Images**: At least 1 image required
- **Maximum Images**: Maximum 10 images allowed
- **Array Validation**: Images field must be an array when provided
- **Owner Verification**: Only the product creator can update images

### Upload Integration
- **Cloudinary Storage**: Uses existing Cloudinary configuration
- **File Processing**: Integrates with `uploadProductImages` and `processCloudinaryFiles` middleware
- **Error Handling**: Includes `handleUploadError` middleware for upload failures

## Usage Examples

### Using Postman

1. **File Upload**:
   - Method: PATCH
   - URL: `{{base_url}}/creators/products/{{product_id}}/images`
   - Body: form-data
   - Key: `productImages` (File type)
   - Select multiple image files

2. **Direct URLs**:
   - Method: PATCH
   - URL: `{{base_url}}/creators/products/{{product_id}}/images`
   - Body: raw JSON
   - Content: `{"images": ["url1", "url2"]}`

### Using JavaScript/Fetch

```javascript
// File upload
const formData = new FormData();
formData.append('productImages', file1);
formData.append('productImages', file2);

const response = await fetch('/api/v1/creators/products/123/images', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`
  },
  body: formData
});

// Direct URLs
const response = await fetch('/api/v1/creators/products/123/images', {
  method: 'PATCH',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    images: ['url1', 'url2']
  })
});
```

## Testing

### Postman Collection
- **File**: `postman/product-images-update.json`
- **Tests**: Includes success cases, error cases, and edge cases
- **Variables**: Uses `{{creator_token}}` and `{{product_id}}`

### Test Script
- **File**: `test-update-images.js`
- **Coverage**: Success scenarios, error validation, limits testing
- **Usage**: Update token and product ID, then run `node test-update-images.js`

## Integration Notes

### Middleware Stack
```javascript
router.patch('/:id/images',
  uploadMiddleware.uploadProductImages,      // Handle file uploads
  uploadMiddleware.processCloudinaryFiles,   // Process uploaded files
  uploadMiddleware.handleUploadError,        // Handle upload errors
  productController.updateImages             // Update product images
);
```

### Database Impact
- **Field Updated**: `product.images` array
- **Cleanup**: Automatic removal of old images from Cloudinary
- **Validation**: Mongoose schema validation applied

### Performance Considerations
- **Image Limits**: 10 image maximum prevents excessive storage usage
- **Async Cleanup**: Image cleanup runs asynchronously to avoid blocking
- **Error Isolation**: Cleanup failures don't affect the main operation

## Related Endpoints
- `PATCH /api/v1/creators/products/:id/basic-info` - Update basic product info
- `PATCH /api/v1/creators/products/:id/specifications` - Update specifications
- `PATCH /api/v1/creators/products/:id/seo` - Update SEO information
- `POST /api/v1/creators/products/:id/variations` - Add product variations (with images)
