const cloudinary = require('cloudinary').v2;

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

/**
 * Upload file to Cloudinary
 * @param {string} filePath - Path to the file to upload
 * @param {object} options - Upload options
 * @returns {Promise<object>} - Cloudinary upload result
 */
const uploadToCloudinary = async (filePath, options = {}) => {
  try {
    const result = await cloudinary.uploader.upload(filePath, {
      resource_type: 'auto', // Automatically detect file type
      ...options
    });
    return result;
  } catch (error) {
    throw new Error(`Cloudinary upload failed: ${error.message}`);
  }
};

/**
 * Delete file from Cloudinary
 * @param {string} publicId - Public ID of the file to delete
 * @param {object} options - Delete options
 * @returns {Promise<object>} - Cloudinary delete result
 */
const deleteFromCloudinary = async (publicId, options = {}) => {
  try {
    const result = await cloudinary.uploader.destroy(publicId, {
      resource_type: 'auto',
      ...options
    });
    return result;
  } catch (error) {
    throw new Error(`Cloudinary delete failed: ${error.message}`);
  }
};

/**
 * Get optimized URL for an image
 * @param {string} publicId - Public ID of the image
 * @param {object} transformations - Image transformations
 * @returns {string} - Optimized image URL
 */
const getOptimizedUrl = (publicId, transformations = {}) => {
  return cloudinary.url(publicId, {
    fetch_format: 'auto',
    quality: 'auto',
    ...transformations
  });
};

/**
 * Generate upload preset configurations for different file types
 */
const uploadPresets = {
  verification: {
    folder: 'everyfash/verification',
    allowed_formats: ['jpg', 'jpeg', 'png', 'pdf'],
    max_file_size: 10000000, // 10MB
    resource_type: 'auto'
  },
  products: {
    folder: 'everyfash/products',
    allowed_formats: ['jpg', 'jpeg', 'png'],
    max_file_size: 5000000, // 5MB
    resource_type: 'image',
    transformation: [
      { width: 1200, height: 1200, crop: 'limit' },
      { quality: 'auto', fetch_format: 'auto' }
    ]
  },
  bales: {
    folder: 'everyfash/bales',
    allowed_formats: ['jpg', 'jpeg', 'png'],
    max_file_size: 5000000, // 5MB
    resource_type: 'image',
    transformation: [
      { width: 1200, height: 1200, crop: 'limit' },
      { quality: 'auto', fetch_format: 'auto' }
    ]
  },
  profiles: {
    folder: 'everyfash/profiles',
    allowed_formats: ['jpg', 'jpeg', 'png'],
    max_file_size: 2000000, // 2MB
    resource_type: 'image',
    transformation: [
      { width: 400, height: 400, crop: 'fill', gravity: 'face' },
      { quality: 'auto', fetch_format: 'auto' }
    ]
  },
  shop: {
    folder: 'everyfash/shop',
    allowed_formats: ['jpg', 'jpeg', 'png'],
    max_file_size: 3000000, // 3MB
    resource_type: 'image',
    transformation: [
      { width: 800, height: 400, crop: 'limit' },
      { quality: 'auto', fetch_format: 'auto' }
    ]
  }
};

/**
 * Extract public ID from Cloudinary URL
 * @param {string} url - Cloudinary URL
 * @returns {string} - Public ID
 */
const extractPublicId = (url) => {
  if (!url || typeof url !== 'string') return null;
  
  // Match Cloudinary URL pattern and extract public ID
  const match = url.match(/\/v\d+\/(.+)\.[^.]+$/);
  return match ? match[1] : null;
};

/**
 * Generate signed upload URL for direct uploads from frontend
 * @param {object} options - Upload options
 * @returns {object} - Signed upload data
 */
const generateSignedUploadUrl = (options = {}) => {
  const timestamp = Math.round(new Date().getTime() / 1000);
  
  const params = {
    timestamp,
    ...options
  };
  
  const signature = cloudinary.utils.api_sign_request(params, process.env.CLOUDINARY_API_SECRET);
  
  return {
    signature,
    timestamp,
    api_key: process.env.CLOUDINARY_API_KEY,
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    ...params
  };
};

module.exports = {
  cloudinary,
  uploadToCloudinary,
  deleteFromCloudinary,
  getOptimizedUrl,
  uploadPresets,
  extractPublicId,
  generateSignedUploadUrl
};
