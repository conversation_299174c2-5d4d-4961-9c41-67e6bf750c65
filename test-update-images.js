const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

/**
 * Test script to verify the updateImages endpoint
 * This tests that the endpoint properly handles image updates
 */

const BASE_URL = 'http://localhost:3000/api/v1';

// Test cases for different image update scenarios
const testCases = [
  {
    name: "Replace all images with new URLs",
    data: {
      operation: "replace",
      images: [
        "https://res.cloudinary.com/demo/image/upload/sample.jpg",
        "https://res.cloudinary.com/demo/image/upload/sample2.jpg"
      ]
    }
  },
  {
    name: "Add new images to existing ones",
    data: {
      operation: "add",
      images: [
        "https://res.cloudinary.com/demo/image/upload/new1.jpg",
        "https://res.cloudinary.com/demo/image/upload/new2.jpg"
      ]
    }
  },
  {
    name: "Remove specific images by URL",
    data: {
      operation: "remove",
      imagesToRemove: [
        "https://res.cloudinary.com/demo/image/upload/sample.jpg"
      ]
    }
  },
  {
    name: "Remove images by index",
    data: {
      operation: "remove",
      imagesToRemove: [0, 2]
    }
  },
  {
    name: "Reorder existing images",
    data: {
      operation: "reorder",
      imageOrder: [
        "https://res.cloudinary.com/demo/image/upload/sample2.jpg",
        "https://res.cloudinary.com/demo/image/upload/sample.jpg"
      ]
    }
  },
  {
    name: "Legacy: Replace without operation (backward compatibility)",
    data: {
      images: ["https://res.cloudinary.com/demo/image/upload/legacy.jpg"]
    }
  }
];

// Error test cases
const errorTestCases = [
  {
    name: "Error: Invalid operation",
    data: { operation: "invalid_operation", images: ["test.jpg"] },
    expectedError: "Invalid operation"
  },
  {
    name: "Error: No images for replace",
    data: { operation: "replace" },
    expectedError: "Please provide at least one image for replacement"
  },
  {
    name: "Error: No images to add",
    data: { operation: "add" },
    expectedError: "Please provide images to add"
  },
  {
    name: "Error: No images to remove specified",
    data: { operation: "remove" },
    expectedError: "Please provide imagesToRemove array"
  },
  {
    name: "Error: No image order specified",
    data: { operation: "reorder" },
    expectedError: "Please provide imageOrder array"
  },
  {
    name: "Error: Images not an array",
    data: { operation: "replace", images: "not-an-array" },
    expectedError: "Images must be an array"
  },
  {
    name: "Error: Too many images (11)",
    data: {
      operation: "replace",
      images: Array.from({length: 11}, (_, i) =>
        `https://res.cloudinary.com/demo/image/upload/sample${i + 1}.jpg`
      )
    },
    expectedError: "Maximum 10 images allowed per product"
  },
  {
    name: "Error: Cannot remove all images",
    data: {
      operation: "remove",
      imagesToRemove: [0, 1, 2, 3, 4] // Assuming product has 5 images
    },
    expectedError: "Cannot remove all images"
  }
];

async function testUpdateImages() {
  console.log('🧪 Testing updateImages Endpoint\n');

  // You'll need to get a valid JWT token for a creator and a product ID
  const authToken = 'YOUR_JWT_TOKEN_HERE'; // Replace with actual token
  const productId = 'YOUR_PRODUCT_ID_HERE'; // Replace with actual product ID
  
  if (authToken === 'YOUR_JWT_TOKEN_HERE' || productId === 'YOUR_PRODUCT_ID_HERE') {
    console.log('❌ Please update the authToken and productId variables with real values');
    console.log('   You can get these from:');
    console.log('   1. Login as a creator to get the JWT token');
    console.log('   2. Create or get an existing product ID');
    return;
  }

  const headers = {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };

  console.log('📝 Running Success Test Cases...\n');

  // Test successful updates
  for (const testCase of testCases) {
    try {
      console.log(`Testing: ${testCase.name}`);
      
      const response = await axios.patch(
        `${BASE_URL}/creators/products/${productId}/images`,
        testCase.data,
        { headers }
      );

      if (response.status === 200) {
        console.log(`✅ ${testCase.name} - SUCCESS`);
        console.log(`   Updated images count: ${response.data.data.product.images.length}`);
        console.log(`   Message: ${response.data.message}\n`);
      } else {
        console.log(`❌ ${testCase.name} - Unexpected status: ${response.status}\n`);
      }
    } catch (error) {
      console.log(`❌ ${testCase.name} - ERROR:`);
      console.log(`   ${error.response?.data?.message || error.message}\n`);
    }
  }

  console.log('📝 Running Error Test Cases...\n');

  // Test error cases
  for (const testCase of errorTestCases) {
    try {
      console.log(`Testing: ${testCase.name}`);
      
      const response = await axios.patch(
        `${BASE_URL}/creators/products/${productId}/images`,
        testCase.data,
        { headers }
      );

      console.log(`❌ ${testCase.name} - Should have failed but got status: ${response.status}\n`);
    } catch (error) {
      if (error.response?.status === 400) {
        const errorMessage = error.response.data.message;
        if (errorMessage.includes(testCase.expectedError)) {
          console.log(`✅ ${testCase.name} - Correctly returned error`);
          console.log(`   Error: ${errorMessage}\n`);
        } else {
          console.log(`❌ ${testCase.name} - Wrong error message`);
          console.log(`   Expected: ${testCase.expectedError}`);
          console.log(`   Got: ${errorMessage}\n`);
        }
      } else {
        console.log(`❌ ${testCase.name} - Unexpected error:`);
        console.log(`   ${error.response?.data?.message || error.message}\n`);
      }
    }
  }

  console.log('🎯 Test Summary:');
  console.log('   - Test both success and error scenarios');
  console.log('   - Verify image count limits (max 10)');
  console.log('   - Check proper error messages');
  console.log('   - Ensure old images are cleaned up automatically');
  console.log('\n📋 Manual Testing Notes:');
  console.log('   1. Test file uploads using Postman with actual image files');
  console.log('   2. Verify Cloudinary cleanup of old images');
  console.log('   3. Test mixed uploads (files + URLs) using form-data');
  console.log('   4. Check that only the product owner can update images');
}

// Run the tests
if (require.main === module) {
  testUpdateImages().catch(console.error);
}

module.exports = { testUpdateImages };
