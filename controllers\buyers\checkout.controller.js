const Cart = require('../../models/cart.model');
const Order = require('../../models/order.model');
const Product = require('../../models/product.model');
const Bale = require('../../models/bale.model');
const Promotion = require('../../models/promotion.model');
const { Buyer } = require('../../models/user.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');

/**
 * Get checkout summary
 * @route GET /api/v1/checkout
 * @access Private
 */
exports.getCheckoutSummary = catchAsync(async (req, res, next) => {
  // Get user's cart
  const cart = await Cart.findOne({ user: req.user.id });
  if (!cart || cart.items.length === 0) {
    return next(new AppError('Your cart is empty', 400));
  }

  // Get user's shipping address
  const buyer = await Buyer.findById(req.user.id).select('shippingAddress');

  // Calculate shipping cost (simplified for now)
  const shippingCost = 50; // Default shipping cost

  // Calculate total with shipping
  const totalWithShipping = cart.total + shippingCost;

  res.status(200).json({
    status: 'success',
    data: {
      cart,
      shippingAddress: buyer.shippingAddress,
      shippingCost,
      totalWithShipping
    }
  });
});

/**
 * Create order
 * @route POST /api/v1/checkout/order
 * @access Private
 */
exports.createOrder = catchAsync(async (req, res, next) => {
  // Get user's cart
  const cart = await Cart.findOne({ user: req.user.id });
  if (!cart || cart.items.length === 0) {
    return next(new AppError('Your cart is empty', 400));
  }

  // Validate shipping address
  if (!req.body.shippingAddress) {
    return next(new AppError('Please provide a shipping address', 400));
  }

  const requiredAddressFields = ['addressLine1', 'city', 'state', 'postalCode', 'country', 'phone'];
  for (const field of requiredAddressFields) {
    if (!req.body.shippingAddress[field]) {
      return next(new AppError(`Please provide ${field} in shipping address`, 400));
    }
  }

  // Validate payment method
  if (!req.body.paymentMethod) {
    return next(new AppError('Please provide a payment method', 400));
  }

  // Validate items stock
  for (const item of cart.items) {
    if (item.type === 'product' && item.product) {
      const product = await Product.findById(item.product);
      if (!product) {
        return next(new AppError(`Product ${item.product} not found`, 404));
      }

      if (product.status !== 'active') {
        return next(new AppError(`Product ${product.name} is not available for purchase`, 400));
      }

      const variation = product.variations.find(
        v => v.color === item.color && v.size === item.size
      );

      if (!variation) {
        return next(new AppError(`Variation for product ${product.name} not found`, 404));
      }

      if (variation.quantity < item.quantity) {
        return next(new AppError(`Not enough stock for ${product.name}. Only ${variation.quantity} available.`, 400));
      }
    } else if (item.type === 'bale' && item.bale) {
      const bale = await Bale.findById(item.bale);
      if (!bale) {
        return next(new AppError(`Bale ${item.bale} not found`, 404));
      }

      if (bale.status !== 'active') {
        return next(new AppError(`Bale ${bale.name} is not available for purchase`, 400));
      }

      const variation = bale.variations.find(v => v.size === item.size);

      if (!variation) {
        return next(new AppError(`Variation for bale ${bale.name} not found`, 404));
      }

      if (variation.quantity < item.quantity) {
        return next(new AppError(`Not enough stock for ${bale.name}. Only ${variation.quantity} available.`, 400));
      }
    }
  }

  // Calculate shipping cost (simplified for now)
  const shippingCost = 50; // Default shipping cost

  // Create order items
  const orderItems = [];

  for (const item of cart.items) {
    let orderItem = {
      type: item.type,
      quantity: item.quantity,
      price: item.price
    };

    if (item.type === 'product' && item.product) {
      const product = await Product.findById(item.product);
      orderItem.product = item.product;
      orderItem.color = item.color;
      orderItem.size = item.size;
      orderItem.name = product.name;
      orderItem.image = product.images[0];
      orderItem.creator = product.creator;

      // Check if item has a promotion
      if (item.promotionId) {
        orderItem.promotion = {
          id: item.promotionId
        };
      }

      // Update product stock
      const variation = product.variations.find(
        v => v.color === item.color && v.size === item.size
      );

      if (variation) {
        variation.quantity -= item.quantity;
        await product.save();
      }
    } else if (item.type === 'bale' && item.bale) {
      const bale = await Bale.findById(item.bale);
      orderItem.bale = item.bale;
      orderItem.size = item.size;
      orderItem.name = bale.name;
      orderItem.image = bale.images[0];
      orderItem.creator = bale.creator;

      // Check if item has a promotion
      if (item.promotionId) {
        orderItem.promotion = {
          id: item.promotionId
        };
      }

      // Update bale stock
      const variation = bale.variations.find(v => v.size === item.size);

      if (variation) {
        variation.quantity -= item.quantity;
        await bale.save();
      }
    }

    orderItems.push(orderItem);
  }

  // Create order
  const order = await Order.create({
    user: req.user.id,
    items: orderItems,
    shippingAddress: req.body.shippingAddress,
    paymentMethod: req.body.paymentMethod,
    status: 'pending',
    trackingNumber: `TR-${Date.now()}-${Math.floor(Math.random() * 1000)}`
  });

  // Calculate subtotal
  order.calculateSubtotal();

  // Set shipping cost and total
  order.shippingCost = shippingCost;
  order.total = order.subtotal + shippingCost;
  await order.save();

  // Apply coupon if exists
  if (cart.coupon) {
    order.coupon = {
      code: cart.coupon.code,
      discount: cart.coupon.discount,
      type: cart.coupon.type
    };

    // Update order total
    order.total = order.total - cart.coupon.discount;
    await order.save();
  }

  // Clear cart
  cart.items = [];
  cart.total = 0;
  cart.coupon = undefined;
  await cart.save();

  res.status(201).json({
    status: 'success',
    data: {
      order
    }
  });
});

/**
 * Process payment
 * @route POST /api/v1/checkout/payment
 * @access Private
 */
exports.processPayment = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.orderId) {
    return next(new AppError('Please provide an order ID', 400));
  }

  // Find order
  const order = await Order.findOne({
    _id: req.body.orderId,
    user: req.user.id
  });

  if (!order) {
    return next(new AppError('No order found with that ID', 404));
  }

  // Check if order is already paid
  if (order.isPaid) {
    return next(new AppError('Order is already paid', 400));
  }

  // In a real application, this would integrate with a payment gateway like Paystack
  // For now, we'll simulate a successful payment

  // Generate a payment reference
  const paymentReference = `PAY-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

  // Calculate fees if they haven't been calculated yet
  if (!order.fees || !order.fees.total) {
    await order.calculateFees();
  }

  // Create a payment record
  const Payment = require('../../models/payment.model');
  const payment = await Payment.createPayment({
    orderId: order._id,
    userId: req.user.id,
    amount: order.total,
    reference: paymentReference,
    channel: req.body.channel || 'card',
    meta: {
      email: req.user.email,
      orderId: order._id
    }
  });

  // Mark payment as successful
  await payment.markAsSuccessful({
    paidAt: Date.now(),
    gatewayResponse: 'Approved'
  });

  // Send order placed notifications
  try {
    const NotificationService = require('../../services/notification.service');

    // Send notification to buyer
    await NotificationService.createOrderNotification({
      order,
      type: 'order_placed',
      recipient: req.user.id
    });

    // Send notifications to creators of items in the order
    const creatorIds = [...new Set(order.items.map(item => item.creator?.toString()).filter(Boolean))];

    for (const creatorId of creatorIds) {
      await NotificationService.createOrderNotification({
        order,
        type: 'order_placed',
        recipient: creatorId
      });
    }
  } catch (error) {
    // Log error but don't stop the checkout process
    console.error('Error sending order placed notification:', error);
  }

  // The order should be updated by the payment.markAsSuccessful method
  // which calls order.markAsPaid

  res.status(200).json({
    status: 'success',
    message: 'Payment processed successfully',
    data: {
      order: await Order.findById(order._id), // Fetch the updated order
      payment
    }
  });
});
