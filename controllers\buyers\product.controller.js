const Product = require('../../models/product.model');
const Bale = require('../../models/bale.model');
const { Buyer } = require('../../models/user.model');
const Category = require('../../models/category.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');



/**
 * Get product details by ID
 * @route GET /api/v1/buyers/products/:id
 * @route GET /api/v1/products/:id
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getProduct = catchAsync(async (req, res, next) => {
  const product = await Product.findById(req.params.id)
    .populate({
      path: 'reviews',
      options: { sort: { createdAt: -1 }, limit: 5 }, // Limit to 5 reviews
      populate: {
        path: 'user',
        select: 'name photo'
      }
    })
    .populate({
      path: 'category',
      select: 'name description'
    })
    .populate({
      path: 'relatedCategories',
      select: 'name description'
    })
    .populate('creator');
  
  if (!product) {
    return next(new AppError('No product found with that ID', 404));
  }

  // Check if product is active
  if (product.status !== 'active') {
    return next(new AppError('This product is not available', 404));
  }

  // Transform category data to include path array
  if (product.category) {
    product.category._doc.pathArray = product.category.name.split(' > ');
  }
  if (product.relatedCategories && product.relatedCategories.length > 0) {
    for (const category of product.relatedCategories) {
      if (category && category.name) {
        category._doc.pathArray = category.name.split(' > ');
      }
    }
  }

  // Add to recently viewed if authenticated
  if (req.user) {
    await Buyer.findByIdAndUpdate(
      req.user.id,
      {
        $push: {
          recentlyViewed: {
            $each: [{ product: product._id, viewedAt: new Date() }],
            $position: 0,
            $slice: 20 // Keep only the 20 most recent items
          }
        }
      },
      { new: true }
    );
  }

  // Get related products based on category and tags
  const relatedProductsQuery = {
    _id: { $ne: product._id }, // Exclude current product
    status: 'active',
    $or: [
      { category: product.category },
      { relatedCategories: { $in: product.relatedCategories } },
      { tags: { $in: product.tags } }
    ]
  };

  const relatedProducts = await Product.find(relatedProductsQuery)
    .select('name images basePrice variations status')
    .sort('-createdAt')
    .limit(6); // Limit to 6 related products

  // Get more products from the same shop/creator
  const shopProductsQuery = {
    _id: { $ne: product._id }, // Exclude current product
    creator: product.creator._id,
    status: 'active'
  };

  const shopProducts = await Product.find(shopProductsQuery)
    .select('name images basePrice variations status')
    .sort('-createdAt')
    .limit(4); // Limit to 4 shop products

  // Extract shop information
  const shopInfo = product.creator ? {
    id: product.creator._id,
    name: product.creator.shopInfo?.name || product.creator.name,
    logo: product.creator.photo,
    isVerified: product.creator.verificationStatus === 'verified' || false,
    metrics: {
      // Core metrics
      averageRating: product.creator.metrics?.averageRating || 0,
      qualityScore: product.creator.metrics?.qualityScore || 0,
      shippingSpeed: product.creator.metrics?.shippingSpeed || 0,

      // Business metrics
      totalProducts: product.creator.metrics?.totalProducts || 0,
      totalBales: product.creator.metrics?.totalBales || 0,
      totalSales: product.creator.metrics?.totalSales || 0,
      totalRevenue: product.creator.metrics?.totalRevenue || 0,

      // Social metrics
      followers: product.creator.metrics?.followers || 0
    },
  } : null;

  // Process variations to include current prices and stock information using virtuals
  const processedVariations = product.variations.map(variation => {
    const variationObj = variation.toObject();
    
    // Get stock status information
    const stockStatus = {
      quantity: variation.quantity,
      isLowStock: variation.isLowStock,
      isOutOfStock: variation.isOutOfStock,
      lastRestocked: variation.lastRestocked
    };
    
    // Get last restock information if available
    const lastRestockInfo = variation.lastRestockInfo;
    if (lastRestockInfo) {
      stockStatus.lastRestockInfo = {
        date: lastRestockInfo.date,
        amount: lastRestockInfo.amount
      };
    }
    
    return {
      ...variationObj,
      currentPrice: variation.currentPrice,
      hasDiscount: variation.hasDiscount,
      discountSource: variation.discountSource,
      discountPercentage: variation.discountPercentage,
      discountEndDate: variation.discountEndDate,
      stockStatus
    };
  });

  // Use the formattedPriceRange virtual for price range information
  const priceRange = product.formattedPriceRange;

  // Get active promotion info using the bestPromotion virtual
  const activePromotion = product.hasActivePromotion ? {
    id: product.bestPromotion._id,
    discountType: product.bestPromotion.discountType,
    discountValue: product.bestPromotion.discountValue,
    startDate: product.bestPromotion.startDate,
    endDate: product.bestPromotion.endDate,
    remainingStock: product.bestPromotion.promoStock
  } : null;

  // Calculate review statistics
  let reviewStats = {
    averageRating: product.ratingsAverage || 0,
    totalReviews: product.ratingsQuantity || 0,
    reviews: product.reviews || []
  };

  // Format related products using virtuals
  const formattedRelatedProducts = relatedProducts.map(product => {
    const productObj = product.toObject();

    return {
      _id: productObj._id,
      name: productObj.name,
      images: productObj.images,
      basePrice: productObj.basePrice,
      status: productObj.status,
      maxDiscountPercentage: product.maxDiscountPercentage
    };
  });

  // Format shop products using virtuals
  const formattedShopProducts = shopProducts.map(product => {
    const productObj = product.toObject();

    return {
      _id: productObj._id,
      name: productObj.name,
      images: productObj.images,
      basePrice: productObj.basePrice,
      status: productObj.status,
      maxDiscountPercentage: product.maxDiscountPercentage
    };
  });

  // Format the response
  const formattedProduct = {
    ...product._doc,
    variations: processedVariations,
    priceRange,
    activePromotion,
    reviewStats,
    shopInfo
  };

  res.status(200).json({
    status: 'success',
    data: {
      product: formattedProduct,
      relatedProducts: formattedRelatedProducts,
      shopProducts: formattedShopProducts
    }
  });
});

/**
 * Get products and bales based on buyer preferences with fallback to general filtering
 * @route GET /api/v1/buyers/products/feed
 * @route GET /api/v1/products
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getProductFeed = catchAsync(async (req, res, next) => {
  // Build base query
  const queryObj = { status: 'active' };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search', 'usePreferences'];

  // Clone request query for filtering
  const filterQuery = { ...req.query };
  excludedFields.forEach(el => delete filterQuery[el]);

  // Check if we should use preferences
  const usePreferences = req.query.usePreferences !== 'false';

  if (usePreferences) {
    // Get buyer preferences
    const buyer = await Buyer.findById(req.user.id);

    if (buyer && buyer.preferences) {
      // Add category filter if preferences include categories
      if (buyer.preferences.categories && buyer.preferences.categories.length > 0) {
        queryObj.$or = [
          { category: { $in: buyer.preferences.categories } },
          { relatedCategories: { $in: buyer.preferences.categories } }
        ];
      }

      // Add size filter if preferences include sizes
      if (buyer.preferences.sizes && buyer.preferences.sizes.length > 0) {
        queryObj['variations.size'] = { $in: buyer.preferences.sizes };
      }

      // Add color filter if preferences include colors
      if (buyer.preferences.colors && buyer.preferences.colors.length > 0) {
        queryObj['variations.color'] = { $in: buyer.preferences.colors };
      }

      // Add price range filter if preferences include price range
      if (buyer.preferences.priceRange) {
        if (buyer.preferences.priceRange.min) {
          queryObj.basePrice = { $gte: buyer.preferences.priceRange.min };
        }
        if (buyer.preferences.priceRange.max) {
          if (queryObj.basePrice) {
            queryObj.basePrice.$lte = buyer.preferences.priceRange.max;
          } else {
            queryObj.basePrice = { $lte: buyer.preferences.priceRange.max };
          }
        }
      }
    }
  }

  // Apply additional filters from request query
  // Advanced filtering
  let queryStr = JSON.stringify(filterQuery);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);
  const parsedQuery = JSON.parse(queryStr);

  // Merge parsed query with preference-based query
  Object.keys(parsedQuery).forEach(key => {
    // Handle special case for basePrice if it already exists from preferences
    if (key === 'basePrice' && queryObj.basePrice) {
      // Merge price ranges
      if (parsedQuery.basePrice.$gte && !queryObj.basePrice.$gte) {
        queryObj.basePrice.$gte = parsedQuery.basePrice.$gte;
      }
      if (parsedQuery.basePrice.$lte && !queryObj.basePrice.$lte) {
        queryObj.basePrice.$lte = parsedQuery.basePrice.$lte;
      }
    } else {
      queryObj[key] = parsedQuery[key];
    }
  });

  let query = Product.find(queryObj);

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: searchRegex },
        { description: searchRegex },
        { brand: searchRegex },
        { tags: searchRegex },
        { 'specifications.mainMaterial': searchRegex }
      ]
    });
  }

  // Filter by price range if not already set by preferences
  if (!queryObj.basePrice && (req.query.minPrice || req.query.maxPrice)) {
    const priceFilter = {};
    if (req.query.minPrice) priceFilter.$gte = parseFloat(req.query.minPrice);
    if (req.query.maxPrice) priceFilter.$lte = parseFloat(req.query.maxPrice);
    query = query.find({ basePrice: priceFilter });
  }

  // Filter by gender
  if (req.query.gender) {
    query = query.find({ gender: req.query.gender });
  }

  // Filter by category if not already set by preferences
  if (!queryObj.$or && req.query.category) {
    query = query.find({
      $or: [
        { category: req.query.category },
        { relatedCategories: req.query.category }
      ]
    });
  }

  // Filter by color if not already set by preferences
  if (!queryObj['variations.color'] && req.query.color) {
    query = query.find({ 'variations.color': req.query.color });
  }

  // Filter by size if not already set by preferences
  if (!queryObj['variations.size'] && req.query.size) {
    query = query.find({ 'variations.size': req.query.size });
  }

  // Filter by brand
  if (req.query.brand) {
    const brandRegex = new RegExp(req.query.brand, 'i');
    query = query.find({ brand: brandRegex });
  }

  // Filter by creator
  if (req.query.creator) {
    query = query.find({ creator: req.query.creator });
  }

  // Count total before applying pagination
  const total = await Product.countDocuments(query);

  // Sorting
  if (req.query.sort) {
    let sortBy;
    switch (req.query.sort) {
      case 'price-asc':
        sortBy = 'basePrice';
        break;
      case 'price-desc':
        sortBy = '-basePrice';
        break;
      case 'name-asc':
        sortBy = 'name';
        break;
      case 'name-desc':
        sortBy = '-name';
        break;
      case 'rating-desc':
        sortBy = '-ratingsAverage';
        break;
      case 'newest':
        sortBy = '-createdAt';
        break;
      case 'popular':
        sortBy = '-sold';
        break;
      default:
        sortBy = req.query.sort.split(',').join(' ');
    }
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query with necessary populations
  const products = await query
    .populate({
      path: 'category',
      select: 'name description'
    })
    .populate({
      path: 'creator',
      select: 'name photo'
    });

  // Build bale query (similar to product query)
  const baleQueryObj = { status: 'active' };

  // Apply the same filters to bales
  if (usePreferences && req.user) {
    const buyer = await Buyer.findById(req.user.id);

    if (buyer && buyer.preferences) {
      // Add category filter if preferences include categories
      if (buyer.preferences.categories && buyer.preferences.categories.length > 0) {
        baleQueryObj.$or = [
          { category: { $in: buyer.preferences.categories } },
          { relatedCategories: { $in: buyer.preferences.categories } }
        ];
      }

      // Add size filter if preferences include sizes
      if (buyer.preferences.sizes && buyer.preferences.sizes.length > 0) {
        baleQueryObj['variations.size'] = { $in: buyer.preferences.sizes };
      }

      // Add price range filter if preferences include price range
      if (buyer.preferences.priceRange) {
        if (buyer.preferences.priceRange.min) {
          baleQueryObj.basePrice = { $gte: buyer.preferences.priceRange.min };
        }
        if (buyer.preferences.priceRange.max) {
          if (!baleQueryObj.basePrice) baleQueryObj.basePrice = {};
          baleQueryObj.basePrice.$lte = buyer.preferences.priceRange.max;
        }
      }
    }
  }

  // Apply any additional filters from the request
  Object.keys(filterQuery).forEach(key => {
    baleQueryObj[key] = filterQuery[key];
  });

  // Count total bales
  const totalBales = await Bale.countDocuments(baleQueryObj);

  // Create bale query
  let baleQuery = Bale.find(baleQueryObj);

  // Apply sorting to bales
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    baleQuery = baleQuery.sort(sortBy);
  } else {
    baleQuery = baleQuery.sort('-createdAt');
  }

  // Apply pagination to bales
  baleQuery = baleQuery.skip(skip).limit(limit);

  // Execute bale query
  const bales = await baleQuery
    .populate({
      path: 'category',
      select: 'name description'
    })
    .populate({
      path: 'creator',
      select: 'name photo'
    });

  // Calculate discount percentages and format products
  const now = new Date();
  const formattedProducts = products.map(product => {
    const productObj = product.toObject();

    // Calculate max discount percentage and find sale end date
    let maxDiscountPercentage = 0;


    if (product.variations && product.variations.length > 0) {
      // Find variations with active sales
      const salesVariations = product.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        now >= v.saleStartDate && now <= v.saleEndDate
      );

      // Calculate max discount percentage
      for (const variation of salesVariations) {
        const discountPercentage = ((variation.price - variation.salePrice) / variation.price) * 100;
        maxDiscountPercentage = Math.max(maxDiscountPercentage, discountPercentage);
      }

    }

    // Create a simplified product object with essential details
    const simplifiedProduct = {
      _id: productObj._id,
      type: 'product',
      name: productObj.name,
      images: productObj.images,
      basePrice: productObj.basePrice,
      status: productObj.status,
      maxDiscountPercentage: Math.round(maxDiscountPercentage),
      ratingsAverage: productObj.ratingsAverage || 0,
      ratingsQuantity: productObj.ratingsQuantity || 0
    };

    return simplifiedProduct;
  });

  // Format bales similar to products
  const formattedBales = bales.map(bale => {
    const baleObj = bale.toObject();

    // Calculate max discount percentage and find sale end date
    let maxDiscountPercentage = 0;


    if (bale.variations && bale.variations.length > 0) {
      // Find variations with active sales
      const salesVariations = bale.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        now >= v.saleStartDate && now <= v.saleEndDate
      );

      // Calculate max discount percentage
      for (const variation of salesVariations) {
        const discountPercentage = ((variation.price - variation.salePrice) / variation.price) * 100;
        maxDiscountPercentage = Math.max(maxDiscountPercentage, discountPercentage);
      }

    }

    // Create a simplified bale object with essential details
    const simplifiedBale = {
      _id: baleObj._id,
      type: 'bale',
      name: baleObj.name,
      images: baleObj.images,
      basePrice: baleObj.basePrice,
      status: baleObj.status,
      country: baleObj.country,
      totalItems: baleObj.totalItems,
      condition: baleObj.condition,
      maxDiscountPercentage: Math.round(maxDiscountPercentage),
      ratingsAverage: baleObj.ratingsAverage || 0,
      ratingsQuantity: baleObj.ratingsQuantity || 0
    };

    return simplifiedBale;
  });

  // Combine products and bales
  const combinedItems = [...formattedProducts, ...formattedBales];

  // Sort combined items by discount percentage (highest first)
  combinedItems.sort((a, b) => b.maxDiscountPercentage - a.maxDiscountPercentage);

  res.status(200).json({
    status: 'success',
    results: combinedItems.length,
    total: total + totalBales,
    page,
    limit,
    totalPages: Math.ceil((total + totalBales) / limit),
    usingPreferences: usePreferences && req.user ? true : false,
    data: {
      items: combinedItems,
      productCount: formattedProducts.length,
      baleCount: formattedBales.length
    }
  });
});



exports.getFlashySales = catchAsync(async (req, res, next) => {
  const now = new Date();

  // Calculate the start and end of the current week
  const getWeekBounds = (date) => {
    const currentDate = new Date(date);
    const day = currentDate.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday

    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - day + (day === 0 ? -6 : 1));
    startOfWeek.setHours(0, 0, 0, 0);

    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);

    return { startOfWeek, endOfWeek };
  };

  let { startOfWeek, endOfWeek } = getWeekBounds(now);

  // Query products and bales with active sales within the current week
  const productQuery = {
    status: 'active',
    variations: {
      $elemMatch: {
        salePrice: { $exists: true },
        saleStartDate: { $lte: endOfWeek },
        saleEndDate: { $gte: startOfWeek }
      }
    }
  };

  const baleQuery = {
    status: 'active',
    variations: {
      $elemMatch: {
        salePrice: { $exists: true },
        saleStartDate: { $lte: endOfWeek },
        saleEndDate: { $gte: startOfWeek }
      }
    }
  };

  // Find products and bales with active sales
  const products = await Product.find(productQuery);
  const bales = await Bale.find(baleQuery);

  // Helper function to calculate hours left
  const calculateHoursLeft = (endDate) => {
    const timeLeftMs = new Date(endDate) - now;
    const hoursLeft = Math.floor(timeLeftMs / (1000 * 60 * 60));
    return hoursLeft;
  };

  // Process products and bales
  const processItems = (items, type) => {
    return items.map(item => {
      const salesVariations = item.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        v.saleStartDate <= endOfWeek && v.saleEndDate >= startOfWeek
      );

      const maxDiscountPercentage = salesVariations.reduce((max, variation) => {
        const discountPercentage = ((variation.price - variation.salePrice) / variation.price) * 100;
        return Math.max(max, discountPercentage);
      }, 0);

      const earliestEndDate = new Date(Math.min(...salesVariations.map(v => v.saleEndDate.getTime())));
      const hoursLeft = calculateHoursLeft(earliestEndDate);

      const isEndingSoon = hoursLeft <= 48;  // Mark as ending soon if within 48 hours

      return {
        _id: item._id,
        type,
        name: item.name,
        images: item.images,
        basePrice: item.basePrice,
        maxDiscountPercentage: Math.round(maxDiscountPercentage),
        saleEndDate: earliestEndDate,
        isEndingSoon,
        hoursLeft,
      };
    });
  };

  // Process both products and bales
  const processedProducts = processItems(products, 'product');
  const processedBales = processItems(bales, 'bale');

  // Combine and sort items by urgency (ending soon first)
  const allItems = [...processedProducts, ...processedBales];
  allItems.sort((a, b) => {
    if (a.isEndingSoon && !b.isEndingSoon) return -1;
    if (!a.isEndingSoon && b.isEndingSoon) return 1;
    return a.hoursLeft - b.hoursLeft; // If both are ending soon, sort by hours left
  });

  // Return the final response with added week start and end date and counts
  res.status(200).json({
    status: 'success',
    weekStartDate: startOfWeek,
    weekEndDate: endOfWeek,
    productCount: processedProducts.length,
    baleCount: processedBales.length,
    results: allItems.length,
    data: allItems
  });
});


/**
 * Get products for a specific category including all its subcategories using path-based matching
 * @route GET /api/v1/buyers/products/category/:id
 * @route GET /api/v1/products/category/:id
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getProductsByCategory = catchAsync(async (req, res, next) => {
  const categoryId = req.params.id;

  // Validate category exists
  const category = await Category.findById(categoryId);
  if (!category) {
    return next(new AppError('Category not found', 404));
  }

  // Get subcategory details for display
  const subcategories = await Category.find({ parent: categoryId })
    .select('name description image');

  // Get the category path
  const categoryPath = category.name;

  // Get pagination parameters
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  // Build query for products in this category and all its subcategories
  // Find all categories that have this category's path as a prefix
  const allCategoriesInPath = await Category.find({
    name: { $regex: `^${categoryPath}` }
  }).select('_id');

  const categoryIds = allCategoriesInPath.map(cat => cat._id);

  // Build query for products in these categories
  const query = {
    status: 'active',
    category: { $in: categoryIds }
  };

  // Apply filters if provided
  if (req.query.minPrice || req.query.maxPrice) {
    const priceFilter = {};
    if (req.query.minPrice) priceFilter.$gte = parseFloat(req.query.minPrice);
    if (req.query.maxPrice) priceFilter.$lte = parseFloat(req.query.maxPrice);
    query.basePrice = priceFilter;
  }

  // Filter by color
  if (req.query.color) {
    query['variations.color'] = req.query.color;
  }

  // Filter by size
  if (req.query.size) {
    query['variations.size'] = req.query.size;
  }

  // Count total products matching the query
  const total = await Product.countDocuments(query);

  // Get sorting parameter
  let sortBy = '-createdAt';
  if (req.query.sort) {
    switch (req.query.sort) {
      case 'price-asc':
        sortBy = 'basePrice';
        break;
      case 'price-desc':
        sortBy = '-basePrice';
        break;
      case 'name-asc':
        sortBy = 'name';
        break;
      case 'name-desc':
        sortBy = '-name';
        break;
      case 'rating-desc':
        sortBy = '-ratingsAverage';
        break;
      case 'newest':
        sortBy = '-createdAt';
        break;
      case 'popular':
        sortBy = '-sold';
        break;
      default:
        sortBy = req.query.sort.split(',').join(' ');
    }
  }

  // Get products with pagination and sorting
  const products = await Product.find(query)
    .populate({
      path: 'category',
      select: 'name description'
    })
    .populate({
      path: 'creator',
      select: 'name photo shopInfo'
    })
    .sort(sortBy)
    .skip(skip)
    .limit(limit);

  // Calculate discount percentages and format products with essential details
  const now = new Date();
  const productsWithDiscountInfo = products.map(product => {
    const productObj = product.toObject();

    // Calculate max discount percentage and find sale end date
    let maxDiscountPercentage = 0;
    let latestEndDate = null;
    let daysLeft = 0;

    if (product.variations && product.variations.length > 0) {
      // Find variations with active sales
      const salesVariations = product.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        now >= v.saleStartDate && now <= v.saleEndDate
      );

      // Calculate max discount percentage
      for (const variation of salesVariations) {
        const discountPercentage = ((variation.price - variation.salePrice) / variation.price) * 100;
        maxDiscountPercentage = Math.max(maxDiscountPercentage, discountPercentage);
      }

      // Find the latest end date
      if (salesVariations.length > 0) {
        latestEndDate = new Date(Math.max(...salesVariations.map(v => v.saleEndDate.getTime())));
        // Calculate days left
        daysLeft = Math.ceil((latestEndDate - now) / (1000 * 60 * 60 * 24));
      }
    }

    // Create simplified product object with essential details
    return {
      _id: productObj._id,
      name: productObj.name,
      images: productObj.images,
      basePrice: productObj.basePrice,
      status: productObj.status,
      maxDiscountPercentage: Math.round(maxDiscountPercentage),
      saleEndDate: latestEndDate,
      daysLeft: daysLeft,
      ratingsAverage: productObj.ratingsAverage || 0,
      ratingsQuantity: productObj.ratingsQuantity || 0
    };
  });

  // Transform category data to include path array and subcategory information
  const categoryData = {
    _id: category._id,
    name: category.name,
    description: category.description,
    pathArray: category.name.split(' > '),
    subcategories: subcategories,
    includesSubcategoryProducts: categoryIds.length > 1 // More than just the main category
  };

  res.status(200).json({
    status: 'success',
    results: products.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      category: categoryData,
      products: productsWithDiscountInfo
    }
  });
});

/**
 * Get featured products
 * @route GET /api/v1/buyers/products/featured
 * @route GET /api/v1/products/featured
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getFeaturedProducts = catchAsync(async (req, res, next) => {
  // Get pagination parameters
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 10;
  const skip = (page - 1) * limit;

  // Build query for featured products
  // Featured products are those marked as featured by admin
  const query = {
    status: 'active',
    featured: true
  };

  // Count total featured products
  const total = await Product.countDocuments(query);

  // Get featured products with pagination
  const products = await Product.find(query)
    .sort('-createdAt')
    .skip(skip)
    .limit(limit);

  // Calculate discount percentages and format products
  const now = new Date();
  const formattedProducts = products.map(product => {
    const productObj = product.toObject();

    // Calculate max discount percentage and find sale end date
    let maxDiscountPercentage = 0;
    let latestEndDate = null;
    let daysLeft = 0;

    if (product.variations && product.variations.length > 0) {
      // Find variations with active sales
      const salesVariations = product.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        now >= v.saleStartDate && now <= v.saleEndDate
      );

      // Calculate max discount percentage
      for (const variation of salesVariations) {
        const discountPercentage = ((variation.price - variation.salePrice) / variation.price) * 100;
        maxDiscountPercentage = Math.max(maxDiscountPercentage, discountPercentage);
      }

      // Find the latest end date
      if (salesVariations.length > 0) {
        latestEndDate = new Date(Math.max(...salesVariations.map(v => v.saleEndDate.getTime())));
        // Calculate days left
        daysLeft = Math.ceil((latestEndDate - now) / (1000 * 60 * 60 * 24));
      }
    }

    // Create simplified product object with essential details
    return {
      _id: productObj._id,
      name: productObj.name,
      images: productObj.images,
      basePrice: productObj.basePrice,
      status: productObj.status,
      maxDiscountPercentage: Math.round(maxDiscountPercentage),
      saleEndDate: latestEndDate,
      daysLeft: daysLeft,
      ratingsAverage: productObj.ratingsAverage || 0,
      ratingsQuantity: productObj.ratingsQuantity || 0,
      featured: productObj.featured || false
    };
  });

  res.status(200).json({
    status: 'success',
    results: formattedProducts.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      products: formattedProducts
    }
  });
});



/**
 * Get all discounted products sorted by highest discount
 * @route GET /api/v1/buyers/products/discounted
 * @route GET /api/v1/products/discounted
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getDiscountedProducts = catchAsync(async (req, res, next) => {
  const now = new Date();

  // Get pagination parameters
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  // Find products with active discounts
  const query = {
    status: 'active',
    variations: {
      $elemMatch: {
        salePrice: { $exists: true },
        saleStartDate: { $lte: now },
        saleEndDate: { $gte: now }
      }
    }
  };

  // Calculate discount percentage for each product using aggregation
  const productsWithDiscount = await Product.aggregate([
    {
      $match: query
    },
    {
      $unwind: '$variations'
    },
    {
      $match: {
        'variations.salePrice': { $exists: true },
        'variations.saleStartDate': { $lte: now },
        'variations.saleEndDate': { $gte: now }
      }
    },
    {
      $addFields: {
        discountPercentage: {
          $multiply: [
            {
              $divide: [
                { $subtract: ['$variations.price', '$variations.salePrice'] },
                '$variations.price'
              ]
            },
            100
          ]
        }
      }
    },
    {
      $group: {
        _id: '$_id',
        name: { $first: '$name' },
        images: { $first: '$images' },
        basePrice: { $first: '$basePrice' },
        status: { $first: '$status' },
        category: { $first: '$category' },
        creator: { $first: '$creator' },
        ratingsAverage: { $first: '$ratingsAverage' },
        ratingsQuantity: { $first: '$ratingsQuantity' },
        maxDiscountPercentage: { $max: '$discountPercentage' }
      }
    },
    {
      $sort: { maxDiscountPercentage: -1 }
    },
    {
      $skip: skip
    },
    {
      $limit: limit
    }
  ]);

  // Count total discounted products
  const totalCount = await Product.aggregate([
    {
      $match: query
    },
    {
      $count: 'total'
    }
  ]);

  const total = totalCount.length > 0 ? totalCount[0].total : 0;

  // Get the full product details to calculate days left
  const productIds = productsWithDiscount.map(p => p._id);
  const fullProducts = await Product.find({ _id: { $in: productIds } });

  // Format products with time left information
  const formattedProducts = productsWithDiscount.map(product => {
    // Find the full product to get variation details
    const fullProduct = fullProducts.find(p => p._id.toString() === product._id.toString());

    // Calculate days left
    let latestEndDate = null;
    let daysLeft = 0;

    if (fullProduct && fullProduct.variations && fullProduct.variations.length > 0) {
      // Find variations with active sales
      const salesVariations = fullProduct.variations.filter(v =>
        v.salePrice && v.saleStartDate && v.saleEndDate &&
        now >= v.saleStartDate && now <= v.saleEndDate
      );

      // Find the latest end date
      if (salesVariations.length > 0) {
        latestEndDate = new Date(Math.max(...salesVariations.map(v => v.saleEndDate.getTime())));
        // Calculate days left
        daysLeft = Math.ceil((latestEndDate - now) / (1000 * 60 * 60 * 24));
      }
    }

    return {
      _id: product._id,
      name: product.name,
      images: product.images,
      basePrice: product.basePrice,
      status: product.status,
      maxDiscountPercentage: Math.round(product.maxDiscountPercentage),
      saleEndDate: latestEndDate,
      daysLeft: daysLeft,
      ratingsAverage: product.ratingsAverage || 0,
      ratingsQuantity: product.ratingsQuantity || 0
    };
  });

  res.status(200).json({
    status: 'success',
    results: formattedProducts.length,
    total,
    page,
    limit,
    totalPages: Math.ceil(total / limit),
    data: {
      products: formattedProducts
    }
  });
});

/**
 * Get products grouped by multiple categories with highest discount first
 * @route GET /api/v1/buyers/products/by-categories
 * @route GET /api/v1/products/by-categories
 * @access Public (works for both authenticated and unauthenticated users)
 */
/**
 * Get filter parameters for products and bales
 * @route GET /api/v1/buyers/products/filter-params
 * @route GET /api/v1/products/filter-params
 * @access Public (works for both authenticated and unauthenticated users)
 */
exports.getFilterParams = catchAsync(async (req, res, next) => {
  // Get all categories
  const categories = await Category.find().select('name description parent');

  // Get all brands from products
  const brands = await Product.distinct('brand');

  // Get all countries from bales
  const countries = await Bale.distinct('country');

  // Get all conditions from bales
  const conditions = await Bale.distinct('condition');

  // Get min and max prices for products
  const productPriceRange = await Product.aggregate([
    {
      $group: {
        _id: null,
        minPrice: { $min: '$basePrice' },
        maxPrice: { $max: '$basePrice' }
      }
    }
  ]);

  // Get min and max prices for bales
  const balePriceRange = await Bale.aggregate([
    {
      $group: {
        _id: null,
        minPrice: { $min: '$basePrice' },
        maxPrice: { $max: '$basePrice' }
      }
    }
  ]);

  // Get all sizes from product variations
  const productSizes = await Product.aggregate([
    { $unwind: '$variations' },
    { $group: { _id: '$variations.size' } },
    { $sort: { _id: 1 } }
  ]);

  // Get all sizes from bale variations
  const baleSizes = await Bale.aggregate([
    { $unwind: '$variations' },
    { $group: { _id: '$variations.size' } },
    { $sort: { _id: 1 } }
  ]);

  // Get all genders from products
  const genders = await Product.distinct('gender');

  // Define available sort options
  const sortOptions = [
    { value: 'createdAt', label: 'Newest First' },
    { value: '-createdAt', label: 'Oldest First' },
    { value: 'basePrice', label: 'Price: Low to High' },
    { value: '-basePrice', label: 'Price: High to Low' },
    { value: '-ratingsAverage', label: 'Highest Rated' },
    { value: '-ratingsQuantity', label: 'Most Reviewed' }
  ];

  // Organize categories into a hierarchical structure
  const categoryTree = [];
  const categoryMap = {};

  // Create a map of categories by ID
  categories.forEach(category => {
    categoryMap[category._id] = {
      _id: category._id,
      name: category.name,
      description: category.description,
      pathArray: category.name.split(' > '),
      children: []
    };
  });

  // Build the category tree
  categories.forEach(category => {
    if (category.parent) {
      // This is a child category
      if (categoryMap[category.parent]) {
        categoryMap[category.parent].children.push(categoryMap[category._id]);
      }
    } else {
      // This is a root category
      categoryTree.push(categoryMap[category._id]);
    }
  });

  // Extract unique sizes from product and bale variations
  const sizes = [...new Set([
    ...productSizes.map(size => size._id),
    ...baleSizes.map(size => size._id)
  ])].filter(size => size); // Remove null/undefined values

  // Determine global price range
  const minProductPrice = productPriceRange.length > 0 ? productPriceRange[0].minPrice : 0;
  const maxProductPrice = productPriceRange.length > 0 ? productPriceRange[0].maxPrice : 1000;
  const minBalePrice = balePriceRange.length > 0 ? balePriceRange[0].minPrice : 0;
  const maxBalePrice = balePriceRange.length > 0 ? balePriceRange[0].maxPrice : 1000;

  const priceRange = {
    min: Math.min(minProductPrice, minBalePrice),
    max: Math.max(maxProductPrice, maxBalePrice)
  };

  res.status(200).json({
    status: 'success',
    data: {
      categories: categoryTree,
      brands: brands.filter(brand => brand), // Remove null/undefined values
      countries: countries.filter(country => country),
      conditions: conditions.filter(condition => condition),
      sizes,
      genders: genders.filter(gender => gender),
      priceRange,
      sortOptions
    }
  });
});

exports.getProductsByCategories = catchAsync(async (req, res, next) => {
  const now = new Date();
  const productsPerCategory = req.query.limit * 1 || 6;

  // Category pagination
  const page = req.query.page * 1 || 1;
  const categoriesPerPage = req.query.categoriesPerPage * 1 || 10;
  const skip = (page - 1) * categoriesPerPage;

  // Get all active categories that have products
  const categoriesWithProducts = await Product.aggregate([
    {
      $match: { status: 'active' }
    },
    {
      $group: {
        _id: '$category',
        count: { $sum: 1 }
      }
    },
    {
      $match: {
        count: { $gt: 0 }
      }
    }
  ]);

  // Get total count of categories with products
  const totalCategories = categoriesWithProducts.length;

  // Get category IDs
  const allCategoryIds = categoriesWithProducts.map(c => c._id);

  // Calculate discount percentage for each product with variations on sale
  const productsWithDiscount = await Product.aggregate([
    {
      $match: {
        status: 'active',
        category: { $in: allCategoryIds },
        variations: {
          $elemMatch: {
            salePrice: { $exists: true },
            saleStartDate: { $lte: now },
            saleEndDate: { $gte: now }
          }
        }
      }
    },
    {
      $unwind: '$variations'
    },
    {
      $match: {
        'variations.salePrice': { $exists: true },
        'variations.saleStartDate': { $lte: now },
        'variations.saleEndDate': { $gte: now }
      }
    },
    {
      $addFields: {
        discountPercentage: {
          $multiply: [
            {
              $divide: [
                { $subtract: ['$variations.price', '$variations.salePrice'] },
                '$variations.price'
              ]
            },
            100
          ]
        }
      }
    },
    {
      $group: {
        _id: '$_id',
        name: { $first: '$name' },
        brand: { $first: '$brand' },
        basePrice: { $first: '$basePrice' },
        images: { $first: '$images' },
        category: { $first: '$category' },
        creator: { $first: '$creator' },
        maxDiscountPercentage: { $max: '$discountPercentage' }
      }
    }
  ]);

  // Create a map of product ID to discount percentage
  const discountMap = {};
  productsWithDiscount.forEach(p => {
    discountMap[p._id.toString()] = p.maxDiscountPercentage;
  });

  // Get category discount info to sort categories
  const categoryDiscountMap = {};
  for (const product of productsWithDiscount) {
    const categoryId = product.category.toString();
    const discount = product.maxDiscountPercentage || 0;

    if (!categoryDiscountMap[categoryId] || categoryDiscountMap[categoryId] < discount) {
      categoryDiscountMap[categoryId] = discount;
    }
  }

  // Sort category IDs by max discount percentage
  const sortedCategoryIds = [...allCategoryIds].sort((a, b) => {
    const discountA = categoryDiscountMap[a.toString()] || 0;
    const discountB = categoryDiscountMap[b.toString()] || 0;
    return discountB - discountA;
  });

  // Apply pagination to category IDs
  const paginatedCategoryIds = sortedCategoryIds.slice(skip, skip + categoriesPerPage);

  // Get Category details including subcategories
  const categoryDetails = await Promise.all(
    paginatedCategoryIds.map(async (categoryId) => {
      const category = await Category.findById(categoryId)
        .select('name description parent')
        .populate({
          path: 'subcategories',
          select: 'name description image'
        });

      return category;
    })
  );

  // Create a map of category details
  const categoryDetailsMap = {};
  categoryDetails.forEach(category => {
    if (category) {
      categoryDetailsMap[category._id.toString()] = category;
    }
  });

  // Get products for each category
  const categoryResults = [];
  for (const categoryId of paginatedCategoryIds) {
    // Get products for this category
    const products = await Product.find({
      status: 'active',
      category: categoryId
    })
    .populate({
      path: 'category',
      select: 'name description'
    })
    .populate({
      path: 'creator',
      select: 'name photo shopInfo'
    })
    .limit(productsPerCategory * 2); // Get more than we need to ensure we have enough after filtering

    // Skip if no products found
    if (products.length === 0) continue;

    // Add discount percentage and format products with essential details
    const productsWithDiscountInfo = products.map(product => {
      const productObj = product.toObject();
      const maxDiscountPercentage = discountMap[product._id.toString()] || 0;

      // Create simplified product object with essential details
      return {
        _id: productObj._id,
        name: productObj.name,
        images: productObj.images,
        basePrice: productObj.basePrice,
        status: productObj.status,
        maxDiscountPercentage: Math.round(maxDiscountPercentage),
        ratingsAverage: productObj.ratingsAverage || 0,
        ratingsQuantity: productObj.ratingsQuantity || 0
      };
    });

    // Sort by discount percentage (highest first)
    productsWithDiscountInfo.sort((a, b) => b.maxDiscountPercentage - a.maxDiscountPercentage);

    // Take only the requested number of products
    const limitedProducts = productsWithDiscountInfo.slice(0, productsPerCategory);

    // Calculate max discount percentage for this category
    const maxDiscountPercentage = limitedProducts.length > 0 ?
      Math.max(...limitedProducts.map(p => p.maxDiscountPercentage || 0)) : 0;

    // Get category details including subcategories
    const categoryDetail = categoryDetailsMap[categoryId.toString()];

    if (categoryDetail) {
      const categoryName = categoryDetail.name;
      const pathArray = categoryName ? categoryName.split(' > ') : [];

      // Get subcategories
      const subcategories = categoryDetail.subcategories || [];

      // Add category info and products to results
      categoryResults.push({
        category: {
          _id: categoryId,
          name: categoryName,
          description: categoryDetail.description,
          pathArray,
          maxDiscountPercentage,
          subcategories: subcategories.map(sub => ({
            _id: sub._id,
            name: sub.name,
            description: sub.description,
            image: sub.image
          }))
        },
        products: limitedProducts
      });
    }
  }

  res.status(200).json({
    status: 'success',
    results: categoryResults.length,
    total: totalCategories,
    page,
    limit: categoriesPerPage,
    totalPages: Math.ceil(totalCategories / categoriesPerPage),
    data: {
      categories: categoryResults
    }
  });
});





