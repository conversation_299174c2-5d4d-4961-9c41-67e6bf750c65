const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const cookieParser = require('cookie-parser');
const path = require('path');
require('dotenv').config();
const passport = require('./config/passport');
const AppError = require('./utils/appError');
const globalErrorHandler = require('./controllers/error.controller');
const { corsOptions } = require('./config/cors.config');



// Import routes
const authRoutes = require('./routes/auth.routes'); 
// const userRoutes = require('./routes/user.routes');
const creatorRoutes = require('./routes/creator.main.routes');
// const buyerRoutes = require('./routes/buyer.main.routes');
const adminRoutes = require('./routes/admin.main.routes');
// const productRoutes = require('./routes/product.routes');
// const searchRoutes = require('./routes/search.routes');
// const walletRoutes = require('./routes/wallet.routes');
// const notificationRoutes = require('./routes/notification.routes');
// const deviceRoutes = require('./routes/device.routes');
const categoryRoutes = require('./routes/category.routes');

// Create Express app
const app = express();

// Trust proxy - important for rate limiting and security when behind reverse proxy
app.set('trust proxy', 1);

// Enable CORS
app.use(cors(corsOptions));

// Set security HTTP headers (after CORS)
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// Development logging
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Rate limiting
const limiter = rateLimit({
  max: 100, // limit each IP to 100 requests per windowMs
  windowMs: 60 * 60 * 1000, // 1 hour
  message: 'Too many requests from this IP, please try again in an hour!',
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  // Skip successful requests to static files
  skip: (req) => {
    return req.url.startsWith('/static') || req.url.startsWith('/public');
  }
});

app.use('/api', limiter);


// Body parser, reading data from body into req.body
app.use(express.json({ limit: '10kb' }));
app.use(express.urlencoded({ extended: true, limit: '10kb' }));
app.use(cookieParser());



// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));


// Initialize Passport
app.use(passport.initialize());


// Routes
app.use('/api/v1/auth', authRoutes);
app.use('/api/v1/creators', creatorRoutes);
app.use('/api/v1/admin', adminRoutes);
// app.use('/api/v1/buyers', buyerRoutes);
// app.use('/api/v1/products', productRoutes);
// app.use('/api/v1/wallet', walletRoutes);
// app.use('/api/v1/notifications', notificationRoutes);
// app.use('/api/v1/devices', deviceRoutes);
// app.use('/api/v1/bales', baleRoutes);
// app.use('/api/v1/orders', orderRoutes);
// app.use('/api/v1/reviews', reviewRoutes);
// app.use('/api/v1/promotions', promotionRoutes);
// app.use('/api/v1/payouts', payoutRoutes);
// app.use('/api/v1/search', searchRoutes);
app.use('/api/v1/categories', categoryRoutes);


app.get('/', (_, res) => {
  res.json({
    status: 'success',
    message: 'Everyfash API is working!',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
}); 


// 404 route - handle undefined routes
app.all('/{*any}', (req, _, next) => {
  next(new AppError(`Can't find ${req.originalUrl} on this server!`, 404));
});

// Global error handling middleware
app.use(globalErrorHandler);

module.exports = app;
