const multer = require('multer');
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const { cloudinary, uploadPresets } = require('../config/cloudinary.config');
const AppError = require('../utils/appError');

/**
 * Create Cloudinary storage for different file types
 */
const createCloudinaryStorage = (preset) => {
  return new CloudinaryStorage({
    cloudinary: cloudinary,
    params: {
      ...uploadPresets[preset],
      public_id: (req, file) => {
        // Generate unique filename with timestamp
        const timestamp = Date.now();
        const random = Math.round(Math.random() * 1E9);
        const originalName = file.originalname.split('.')[0];
        return `${originalName}-${timestamp}-${random}`;
      }
    }
  });
};

/**
 * File filter function
 */
const createFileFilter = (allowedFormats) => {
  return (req, file, cb) => {
    // Check file extension
    const fileExtension = file.originalname.split('.').pop().toLowerCase();
    
    if (allowedFormats.includes(fileExtension)) {
      cb(null, true);
    } else {
      cb(new AppError(`Only ${allowedFormats.join(', ')} files are allowed!`, 400), false);
    }
  };
};

/**
 * Create multer upload middleware for different file types
 */
const createUploadMiddleware = (preset, maxCount = 1, fieldName = 'file') => {
  const storage = createCloudinaryStorage(preset);
  const fileFilter = createFileFilter(uploadPresets[preset].allowed_formats);
  
  const upload = multer({
    storage: storage,
    limits: { 
      fileSize: uploadPresets[preset].max_file_size 
    },
    fileFilter: fileFilter
  });

  if (maxCount === 1) {
    return upload.single(fieldName);
  } else {
    return upload.array(fieldName, maxCount);
  }
};

/**
 * Upload middlewares for different file types
 */

// Verification documents upload (multiple files)
exports.uploadVerificationDocuments = createUploadMiddleware('verification', 5, 'verificationDocuments');

// Product images upload (multiple files)
exports.uploadProductImages = createUploadMiddleware('products', 10, 'productImages');

// Bale images upload (multiple files)
exports.uploadBaleImages = createUploadMiddleware('bales', 10, 'baleImages');

// Profile photo upload (single file)
exports.uploadProfilePhoto = createUploadMiddleware('profiles', 1, 'profilePhoto');

// Shop media upload (multiple fields)
exports.uploadShopMedia = multer({
  storage: createCloudinaryStorage('shop'),
  limits: { fileSize: uploadPresets.shop.max_file_size },
  fileFilter: createFileFilter(uploadPresets.shop.allowed_formats)
}).fields([
  { name: 'logo', maxCount: 1 },
  { name: 'banner', maxCount: 1 }
]);

/**
 * Process uploaded files and add URLs to req.body
 */
exports.processCloudinaryFiles = (req, res, next) => {
  try {
    // For multiple files (arrays)
    if (req.files && Array.isArray(req.files)) {
      const fieldName = req.files[0]?.fieldname;
      const fileUrls = req.files.map(file => file.path);

      // Add file URLs to req.body based on field name
      if (fieldName === 'verificationDocuments') {
        req.body.verificationDocuments = fileUrls;
      } else if (fieldName === 'productImages') {
        req.body.images = fileUrls;
      } else if (fieldName === 'baleImages') {
        req.body.images = fileUrls;
      }
    }
    // For multiple fields (object)
    else if (req.files && typeof req.files === 'object') {
      Object.keys(req.files).forEach(fieldName => {
        if (req.files[fieldName] && req.files[fieldName].length > 0) {
          const fileUrl = req.files[fieldName][0].path;
          req.body[fieldName] = fileUrl;
        }
      });
    }
    // For single file
    else if (req.file) {
      const fileUrl = req.file.path;
      
      if (req.file.fieldname === 'profilePhoto') {
        req.body.photo = fileUrl;
      }
    }

    next();
  } catch (error) {
    next(new AppError('Error processing uploaded files', 500));
  }
};

/**
 * Error handler for multer/cloudinary errors
 */
exports.handleUploadError = (err, req, res, next) => {
  if (err instanceof multer.MulterError) {
    if (err.code === 'LIMIT_FILE_SIZE') {
      return next(new AppError('File too large. Please check the file size limits.', 400));
    }
    if (err.code === 'LIMIT_FILE_COUNT') {
      return next(new AppError('Too many files. Please check the file count limits.', 400));
    }
    return next(new AppError(`Upload error: ${err.message}`, 400));
  }

  // Cloudinary errors
  if (err.message && err.message.includes('Cloudinary')) {
    return next(new AppError('File upload failed. Please try again.', 500));
  }

  next(err);
};

/**
 * Middleware for processing form data with nested fields
 * (Keep the same logic as the original middleware)
 */
exports.processNestedFormData = (req, res, next) => {
  if (!req.body || Object.keys(req.body).length === 0) {
    return next();
  }

  // Process form data for nested objects
  const processedBody = {};

  // Process each field in the request body
  Object.keys(req.body).forEach(key => {
    // Check if the key contains brackets (indicating a nested field)
    if (key.includes('[') && key.includes(']')) {
      // Extract the parent key and child key
      const matches = key.match(/([^\[]+)\[([^\]]+)\]/);
      if (matches && matches.length === 3) {
        const parentKey = matches[1];
        const childKey = matches[2];

        // Initialize the parent object if it doesn't exist
        if (!processedBody[parentKey]) {
          processedBody[parentKey] = {};
        }

        // Set the child property
        processedBody[parentKey][childKey] = req.body[key];
      }
    } else {
      // For non-nested fields, copy as is
      processedBody[key] = req.body[key];
    }
  });

  // Special handling for arrays
  const arrayFields = {};
  Object.keys(req.body).forEach(key => {
    if (key.endsWith('[]')) {
      const fieldName = key.slice(0, -2);
      if (!arrayFields[fieldName]) {
        arrayFields[fieldName] = [];
      }
      arrayFields[fieldName].push(req.body[key]);
    }
  });

  // Add processed arrays to the processed body
  Object.keys(arrayFields).forEach(key => {
    processedBody[key] = arrayFields[key];
  });

  // Handle shipping methods as array if it's a string
  if (processedBody.methods && typeof processedBody.methods === 'string') {
    try {
      processedBody.methods = JSON.parse(processedBody.methods);
    } catch (err) {
      // Error parsing shipping methods - continue with original value
    }
  }

  // Replace req.body with processed body
  req.body = { ...req.body, ...processedBody };

  next();
};
