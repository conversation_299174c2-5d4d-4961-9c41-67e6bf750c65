const mongoose = require('mongoose');
const { BaseUser } = require('../models/user.model');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.DATABASE_URL || 'mongodb://localhost:27017/everyfash');
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Debug user password change status
const debugUsers = async () => {
  try {
    console.log('=== USER DEBUG REPORT ===\n');
    
    // Get total user count
    const totalUsers = await BaseUser.countDocuments();
    console.log(`Total users: ${totalUsers}`);
    
    // Users with passwordChangedAt set
    const usersWithPasswordChangedAt = await BaseUser.countDocuments({
      passwordChangedAt: { $exists: true, $ne: null }
    });
    console.log(`Users with passwordChangedAt set: ${usersWithPasswordChangedAt}`);
    
    // Users without passwordChangedAt
    const usersWithoutPasswordChangedAt = await BaseUser.countDocuments({
      $or: [
        { passwordChangedAt: { $exists: false } },
        { passwordChangedAt: null }
      ]
    });
    console.log(`Users without passwordChangedAt: ${usersWithoutPasswordChangedAt}`);
    
    console.log('\n=== DETAILED USER ANALYSIS ===\n');
    
    // Get sample users with passwordChangedAt
    const sampleUsersWithPasswordChangedAt = await BaseUser.find({
      passwordChangedAt: { $exists: true, $ne: null }
    })
    .select('_id email passwordChangedAt createdAt role')
    .limit(5)
    .lean();
    
    console.log('Sample users WITH passwordChangedAt:');
    sampleUsersWithPasswordChangedAt.forEach(user => {
      const timeDiff = user.passwordChangedAt.getTime() - user.createdAt.getTime();
      console.log(`- ${user.email} (${user.role})`);
      console.log(`  Created: ${user.createdAt}`);
      console.log(`  Password changed: ${user.passwordChangedAt}`);
      console.log(`  Time diff: ${timeDiff}ms (${Math.round(timeDiff / 1000)}s)`);
      console.log('');
    });
    
    // Get sample users without passwordChangedAt
    const sampleUsersWithoutPasswordChangedAt = await BaseUser.find({
      $or: [
        { passwordChangedAt: { $exists: false } },
        { passwordChangedAt: null }
      ]
    })
    .select('_id email passwordChangedAt createdAt role')
    .limit(5)
    .lean();
    
    console.log('Sample users WITHOUT passwordChangedAt:');
    sampleUsersWithoutPasswordChangedAt.forEach(user => {
      console.log(`- ${user.email} (${user.role})`);
      console.log(`  Created: ${user.createdAt}`);
      console.log(`  Password changed: ${user.passwordChangedAt || 'NOT SET'}`);
      console.log('');
    });
    
    // Check for users with passwordChangedAt in the future
    const now = new Date();
    const usersWithFuturePasswordChange = await BaseUser.find({
      passwordChangedAt: { $gt: now }
    }).select('_id email passwordChangedAt').lean();
    
    if (usersWithFuturePasswordChange.length > 0) {
      console.log('⚠️  USERS WITH FUTURE PASSWORD CHANGE DATES:');
      usersWithFuturePasswordChange.forEach(user => {
        console.log(`- ${user.email}: ${user.passwordChangedAt}`);
      });
      console.log('');
    }
    
    // Check for users with passwordChangedAt very close to createdAt
    const problematicUsers = await BaseUser.aggregate([
      {
        $match: {
          passwordChangedAt: { $exists: true, $ne: null }
        }
      },
      {
        $addFields: {
          timeDiff: {
            $abs: {
              $subtract: ['$passwordChangedAt', '$createdAt']
            }
          }
        }
      },
      {
        $match: {
          timeDiff: { $lt: 60000 } // Less than 1 minute
        }
      },
      {
        $project: {
          email: 1,
          passwordChangedAt: 1,
          createdAt: 1,
          timeDiff: 1
        }
      }
    ]);
    
    if (problematicUsers.length > 0) {
      console.log('⚠️  USERS WITH PASSWORDCHANGEDAT SET DURING REGISTRATION:');
      problematicUsers.forEach(user => {
        console.log(`- ${user.email}: ${user.timeDiff}ms difference`);
      });
      console.log('');
    }
    
  } catch (error) {
    console.error('Error debugging users:', error);
  }
};

// Main function
const main = async () => {
  await connectDB();
  await debugUsers();
  await mongoose.disconnect();
  console.log('Debug completed');
};

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { debugUsers };
