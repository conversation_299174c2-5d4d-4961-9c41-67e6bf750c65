const Order = require('../../models/order.model');
const Product = require('../../models/product.model');
const Bale = require('../../models/bale.model');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');
const NotificationService = require('../../services/notification.service');

/**
 * Exchange an item in an order
 * @route POST /api/v1/admin/orders/:orderId/exchange
 * @access Private (Admin only)
 */
exports.exchangeOrderItem = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.itemId) {
    return next(new AppError('Item ID is required', 400));
  }

  if (!req.body.newItem) {
    return next(new AppError('New item data is required', 400));
  }

  // Validate that either product/bale ID or variationId is provided
  if (req.body.newItem.type === 'product' && !req.body.newItem.product && !req.body.newItem.variationId) {
    return next(new AppError('Either product ID or variation ID is required for product exchanges', 400));
  }

  if (req.body.newItem.type === 'bale' && !req.body.newItem.bale && !req.body.newItem.variationId) {
    return next(new AppError('Either bale ID or variation ID is required for bale exchanges', 400));
  }

  // Find the order
  const order = await Order.findById(req.params.orderId);
  if (!order) {
    return next(new AppError('Order not found', 404));
  }

  // Check if order is in a valid state for exchange
  const validStates = ['processing', 'shipped', 'delivered'];
  if (!validStates.includes(order.status)) {
    return next(new AppError(`Cannot exchange items in an order with status: ${order.status}`, 400));
  }

  // Find the item to exchange
  const itemToExchange = order.items.id(req.body.itemId);
  if (!itemToExchange) {
    return next(new AppError(`Item with ID ${req.body.itemId} not found in order`, 404));
  }

  // If the new item doesn't specify a creator, use the same creator as the original item
  if (!req.body.newItem.creator && itemToExchange.creator) {
    req.body.newItem.creator = itemToExchange.creator;
  }

  // Check if the exchange is between items from the same creator
  const sameCreator =
    (itemToExchange.creator && req.body.newItem.creator &&
     itemToExchange.creator.toString() === req.body.newItem.creator.toString());

  // Warn if exchanging between different creators
  if (!sameCreator && itemToExchange.creator && req.body.newItem.creator) {
    console.warn(`Exchange between different creators: ${itemToExchange.creator} -> ${req.body.newItem.creator}`);
  }

  try {
    // Process the exchange
    await order.exchangeItem({
      itemId: req.body.itemId,
      newItem: req.body.newItem,
      reason: req.body.reason || 'Item exchanged by admin',
      initiatedBy: req.user.id
    });

    // Send notifications
    try {
      // Notify buyer
      await NotificationService.createNotification({
        recipient: order.user,
        type: 'order_exchange',
        title: 'Item Exchanged in Your Order',
        message: `An item in your order #${order.orderNumber} has been exchanged. Please check your order details.`,
        priority: 'high',
        data: {
          order: order._id,
          url: `/orders/${order._id}`
        }
      });

      // Get the creators involved
      const oldItem = order.items.id(req.body.itemId);
      const newItem = order.items[order.items.length - 1]; // The newly added item is the last one

      // Notify old item creator if different from new item creator
      if (oldItem.creator && (!newItem.creator || oldItem.creator.toString() !== newItem.creator.toString())) {
        await NotificationService.createNotification({
          recipient: oldItem.creator,
          type: 'order_exchange',
          title: 'Item Exchanged in an Order',
          message: `Your item in order #${order.orderNumber} has been exchanged for another item.`,
          priority: 'medium',
          data: {
            order: order._id,
            url: `/dashboard/orders/${order._id}`
          }
        });
      }

      // Notify new item creator if different from old item creator
      if (newItem.creator && (!oldItem.creator || newItem.creator.toString() !== oldItem.creator.toString())) {
        await NotificationService.createNotification({
          recipient: newItem.creator,
          type: 'order_exchange',
          title: 'New Item Added to an Order',
          message: `Your item has been added to order #${order.orderNumber} as part of an exchange.`,
          priority: 'medium',
          data: {
            order: order._id,
            url: `/dashboard/orders/${order._id}`
          }
        });
      }
    } catch (error) {
      console.error('Error sending exchange notifications:', error);
      // Continue even if notifications fail
    }

    res.status(200).json({
      status: 'success',
      data: {
        order
      }
    });
  } catch (error) {
    return next(new AppError(error.message, 400));
  }
});

/**
 * Get product variations for exchange
 * @route GET /api/v1/admin/exchange/product/:productId/variations
 * @access Private (Admin only)
 */
exports.getProductVariations = catchAsync(async (req, res, next) => {
  const { productId } = req.params;

  // Find the product
  const product = await Product.findById(productId);
  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  // Get available variations with stock > 0
  const availableVariations = product.variations.filter(v => v.quantity > 0);

  res.status(200).json({
    status: 'success',
    data: {
      product: {
        _id: product._id,
        name: product.name,
        images: product.images,
        price: product.price,
        creator: product.creator
      },
      variations: availableVariations
    }
  });
});

/**
 * Get product variations for exchange
 * @route GET /api/v1/admin/exchange/products/:productId/variations
 * @access Private (Admin only)
 */
exports.getProductVariations = catchAsync(async (req, res, next) => {
  const product = await Product.findById(req.params.productId);

  if (!product) {
    return next(new AppError('Product not found', 404));
  }

  // Filter variations with stock > 0
  const availableVariations = product.variations.filter(v => v.quantity > 0);

  res.status(200).json({
    status: 'success',
    results: availableVariations.length,
    data: {
      product: {
        _id: product._id,
        name: product.name,
        images: product.images,
        price: product.price,
        creator: product.creator
      },
      variations: availableVariations
    }
  });
});

/**
 * Get available products for exchange
 * @route GET /api/v1/admin/exchange/products
 * @access Private (Admin only)
 */
exports.getExchangeProducts = catchAsync(async (req, res, next) => {
  // Get query parameters
  const { search, category, minPrice, maxPrice, page = 1, limit = 20 } = req.query;

  // Build query
  const query = { status: 'active' };

  // Add search filter
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } }
    ];
  }

  // Add category filter
  if (category) {
    query.category = category;
  }

  // Add price filter
  if (minPrice || maxPrice) {
    query.price = {};
    if (minPrice) query.price.$gte = parseFloat(minPrice);
    if (maxPrice) query.price.$lte = parseFloat(maxPrice);
  }

  // Execute query with pagination
  const skip = (page - 1) * limit;

  const products = await Product.find(query)
    .select('name images price variations category creator')
    .populate('creator', 'name shopInfo.businessName')
    .skip(skip)
    .limit(parseInt(limit))
    .sort({ createdAt: -1 });

  // Get total count for pagination
  const total = await Product.countDocuments(query);

  res.status(200).json({
    status: 'success',
    results: products.length,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit)
    },
    data: {
      products
    }
  });
});

/**
 * Get bale variations for exchange
 * @route GET /api/v1/admin/exchange/bales/:baleId/variations
 * @access Private (Admin only)
 */
exports.getBaleVariations = catchAsync(async (req, res, next) => {
  const bale = await Bale.findById(req.params.baleId);

  if (!bale) {
    return next(new AppError('Bale not found', 404));
  }

  // Filter variations with stock > 0
  const availableVariations = bale.variations.filter(v => v.quantity > 0);

  res.status(200).json({
    status: 'success',
    results: availableVariations.length,
    data: {
      bale: {
        _id: bale._id,
        name: bale.name,
        images: bale.images,
        price: bale.price,
        creator: bale.creator
      },
      variations: availableVariations
    }
  });
});

/**
 * Get available bales for exchange
 * @route GET /api/v1/admin/exchange/bales
 * @access Private (Admin only)
 */
exports.getExchangeBales = catchAsync(async (req, res, next) => {
  // Get query parameters
  const { search, country, minPrice, maxPrice, page = 1, limit = 20 } = req.query;

  // Build query
  const query = { status: 'active' };

  // Add search filter
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } }
    ];
  }

  // Add country filter
  if (country) {
    query.country = country;
  }

  // Add price filter
  if (minPrice || maxPrice) {
    query.price = {};
    if (minPrice) query.price.$gte = parseFloat(minPrice);
    if (maxPrice) query.price.$lte = parseFloat(maxPrice);
  }

  // Execute query with pagination
  const skip = (page - 1) * limit;

  const bales = await Bale.find(query)
    .select('name images price variations country creator')
    .populate('creator', 'name shopInfo.businessName')
    .skip(skip)
    .limit(parseInt(limit))
    .sort({ createdAt: -1 });

  // Get total count for pagination
  const total = await Bale.countDocuments(query);

  res.status(200).json({
    status: 'success',
    results: bales.length,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      pages: Math.ceil(total / limit)
    },
    data: {
      bales
    }
  });
});
