{"info": {"_postman_id": "product-images-update-2024", "name": "Product Images Update", "description": "Test collection for updating product images endpoint", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{creator_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "creator_token", "value": "", "type": "string"}, {"key": "product_id", "value": "", "type": "string"}], "item": [{"name": "Update Product Images - File Upload", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "productImages", "type": "file", "src": [], "description": "Upload new product images (max 10 files)"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Update Product Images - Direct URLs", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"images\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product1.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product2.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product3.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Update Product Images - Mixed (Upload + URLs)", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "productImages", "type": "file", "src": [], "description": "Upload new images"}, {"key": "images", "value": "[\"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/existing-image.jpg\"]", "type": "text", "description": "Existing image URLs to keep"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Update Product Images - Error: No Images", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Update Product Images - Error: Too Many Images", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"images\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image1.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image2.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image3.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image4.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image5.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image6.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image7.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image8.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image9.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image10.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image11.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Update Product Images - Error: Invalid Images Format", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"images\": \"not-an-array\"\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}]}