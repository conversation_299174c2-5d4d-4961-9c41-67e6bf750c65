{"info": {"_postman_id": "product-images-update-2024", "name": "Product Images Update", "description": "Test collection for updating product images endpoint", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{creator_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "creator_token", "value": "", "type": "string"}, {"key": "product_id", "value": "", "type": "string"}], "item": [{"name": "Image Operations", "item": [{"name": "Replace All Images - File Upload", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "operation", "value": "replace", "type": "text"}, {"key": "productImages", "type": "file", "src": [], "description": "Upload new product images (max 10 files)"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Replace All Images - Direct URLs", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"replace\",\n  \"images\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product1.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product2.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product3.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Add New Images - File Upload", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "operation", "value": "add", "type": "text"}, {"key": "productImages", "type": "file", "src": [], "description": "Upload additional images to add"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Add New Images - URLs", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"add\",\n  \"images\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/new-image1.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/new-image2.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Remove Specific Images - By URL", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"remove\",\n  \"imagesToRemove\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/old-image1.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/old-image2.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Remove Specific Images - By Index", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"remove\",\n  \"imagesToRemove\": [0, 2]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Reorder Images", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"reorder\",\n  \"imageOrder\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image3.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image1.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/image2.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}]}, {"name": "Legacy Support (Backward Compatibility)", "item": [{"name": "Update Product Images - Direct URLs (Legacy)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"images\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product1.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product2.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/product3.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Update Product Images - Mixed (Upload + URLs) (Legacy)", "request": {"method": "PATCH", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "productImages", "type": "file", "src": [], "description": "Upload new images"}, {"key": "images", "value": "[\"https://res.cloudinary.com/your-cloud/image/upload/v1234567890/existing-image.jpg\"]", "type": "text", "description": "Existing image URLs to keep"}]}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}]}, {"name": "Error Test Cases", "item": [{"name": "Error: Invalid Operation", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"invalid_operation\",\n  \"images\": [\"https://example.com/image.jpg\"]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Error: No Images for Replace", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"replace\"\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Error: No Images to Add", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"add\"\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Error: No Images to Remove", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"remove\"\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Error: <PERSON><PERSON><PERSON>", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"reorder\",\n  \"imageOrder\": [\"https://wrong-url.jpg\"]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Error: Too Many Images", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"replace\",\n  \"images\": [\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image1.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image2.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image3.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image4.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image5.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image6.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image7.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image8.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image9.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image10.jpg\",\n    \"https://res.cloudinary.com/your-cloud/image/upload/v1/image11.jpg\"\n  ]\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}, {"name": "Error: Invalid Images Format", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"operation\": \"replace\",\n  \"images\": \"not-an-array\"\n}"}, "url": {"raw": "{{base_url}}/creators/products/{{product_id}}/images", "host": ["{{base_url}}"], "path": ["creators", "products", "{{product_id}}", "images"]}}, "response": []}]}]}