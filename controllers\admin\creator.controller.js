const mongoose = require('mongoose');
const { Creator } = require('../../models/user.model');
const Product = require('../../models/product.model');
const Bale = require('../../models/bale.model');
const Order = require('../../models/order.model');
const Review = require('../../models/review.model');
const sendEmail = require('../../utils/sendEmail');
const AppError = require('../../utils/appError');
const catchAsync = require('../../utils/catchAsync');
const crypto = require('crypto'); // Maybe missing or wrongly importing


/**
 * Get all creators
 * @route GET /api/v1/admin/creators
 * @access Private (Admin only)
 */
exports.getAllCreators = catchAsync(async (req, res, next) => {
  // Build query
  const queryObj = { ...req.query };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);

  let query = Creator.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: searchRegex },
        { email: searchRegex },
        { 'businessInfo.businessName': searchRegex },
        { 'shopInfo.name': searchRegex }
      ]
    });
  }

  // Filter by verification status if specified
  if (req.query.verificationStatus) {
    query = query.find({ verificationStatus: req.query.verificationStatus });
  }

  // Filter by onboarding status if specified
  if (req.query.onboardingStatus) {
    query = query.find({ onboardingStatus: req.query.onboardingStatus });
  }

  // Count total before applying pagination
  const total = await Creator.countDocuments(query);

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const creators = await query;

  res.status(200).json({
    status: 'success',
    results: creators.length,
    total,
    page,
    limit,
    data: {
      creators
    }
  });
});


/**
 * Reset creator password by admin
 * @route POST /api/v1/admin/creators/:id/reset-password
 * @access Private (Admin only)
 */
exports.resetCreatorPassword = catchAsync(async (req, res, next) => {
  // Find creator
  const creator = await Creator.findById(req.params.id);

  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Generate a random password
  const tempPassword = crypto.randomBytes(8).toString('hex');

  // Update creator with new password
  creator.password = tempPassword;
  creator.passwordConfirm = tempPassword;
  creator.passwordChangedAt = Date.now();

  await creator.save();

  // Send email with temporary password
   // 4. Prepare the email
   const htmlMessage = `
   <p>Your password has been reset by an administrator.</p>
   <p>Your temporary password is: <strong>${tempPassword}</strong></p>
   <p>Please log in and change your password immediately.</p>
 `;

  const plainMessage = `
    Your password has been reset by an administrator.\n
    Your temporary password is: ${tempPassword}\n
    Please log in and change your password immediately.
  `;

  try {
    await sendEmail({
      email: creator.email,
      subject: 'Your Password Has Been Reset',
      message: plainMessage, // plaintext version
      html: htmlMessage      // html version
    });

    res.status(200).json({
      status: 'success',
      message: 'Password reset successfully. Temporary password has been sent to the creator\'s email.',
      data: {
        tempPassword
      }
    });
  } catch (err) {
    // If there was an error sending the email, return the temporary password in the response
    // This is not ideal for security but ensures the admin can still communicate the password
    res.status(200).json({
      status: 'success',
      message: 'Password reset successfully, but email could not be sent.',
      data: {
        tempPassword
      }
    });
  }
});


/**
 * Update creator details by admin
 * @route PATCH /api/v1/admin/creators/:id/details
 * @access Private (Admin only)
 */
exports.updateCreatorDetails = catchAsync(async (req, res, next) => {
  // Create a filtered body to prevent unwanted fields
  const filteredBody = {};
  const allowedFields = ['name', 'email', 'phone', 'gender', 'dateOfBirth', 'active'];

  Object.keys(req.body).forEach(key => {
    if (allowedFields.includes(key)) {
      filteredBody[key] = req.body[key];
    }
  });

  // Find and update creator
  const updatedCreator = await Creator.findByIdAndUpdate(
    req.params.id,
    filteredBody,
    {
      new: true,
      runValidators: true
    }
  ).select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  if (!updatedCreator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      creator: updatedCreator
    }
  });
});

/**
 * Update creator metrics by admin
 * @route PATCH /api/v1/admin/creators/:id/metrics
 * @access Private (Admin only)
 */
exports.updateCreatorMetrics = catchAsync(async (req, res, next) => {
  // Find creator
  const creator = await Creator.findById(req.params.id);

  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Create a filtered body to prevent unwanted fields
  const filteredMetrics = {};
  const allowedMetrics = ['averageRating', 'qualityScore', 'shippingSpeed'];

  // Validate and filter metrics
  Object.keys(req.body).forEach(key => {
    if (allowedMetrics.includes(key)) {
      // Validate ranges
      if (key === 'averageRating') {
        const rating = parseFloat(req.body[key]);
        if (isNaN(rating) || rating < 0 || rating > 5) {
          return next(new AppError('Average rating must be between 0 and 5', 400));
        }
        filteredMetrics[`metrics.${key}`] = rating;
      } else if (key === 'qualityScore') {
        const score = parseFloat(req.body[key]);
        if (isNaN(score) || score < 0 || score > 100) {
          return next(new AppError('Quality score must be between 0 and 100', 400));
        }
        filteredMetrics[`metrics.${key}`] = score;
      } else if (key === 'shippingSpeed') {
        const speed = parseFloat(req.body[key]);
        if (isNaN(speed) || speed < 0) {
          return next(new AppError('Shipping speed cannot be negative', 400));
        }
        filteredMetrics[`metrics.${key}`] = speed;
      }
    }
  });

  // Check if there are metrics to update
  if (Object.keys(filteredMetrics).length === 0) {
    return next(new AppError('No valid metrics provided for update', 400));
  }

  // Update creator metrics
  const updatedCreator = await Creator.findByIdAndUpdate(
    req.params.id,
    filteredMetrics,
    {
      new: true,
      runValidators: true
    }
  ).select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  res.status(200).json({
    status: 'success',
    data: {
      creator: updatedCreator
    }
  });
});


/**
 * Get creator by ID
 * @route GET /api/v1/admin/creators/:id
 * @access Private (Admin only)
 */
exports.getCreator = catchAsync(async (req, res, next) => {
  const creator = await Creator.findById(req.params.id)
    .select('-__v -password -passwordChangedAt -passwordResetToken -passwordResetExpires');

  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  res.status(200).json({
    status: 'success',
    data: {
      creator
    }
  });
});

/**
 * Update creator verification status
 * @route PATCH /api/v1/admin/creators/:id/verification
 * @access Private (Admin only)
 */
exports.updateVerificationStatus = catchAsync(async (req, res, next) => {
  // Validate request body
  if (!req.body.verificationStatus || !['unverified', 'pending', 'verified', 'rejected'].includes(req.body.verificationStatus)) {
    return next(new AppError('Please provide a valid verification status (unverified, pending, verified, rejected)', 400));
  }

  // Find creator
  const creator = await Creator.findById(req.params.id);

  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Update verification status
  creator.verificationStatus = req.body.verificationStatus;

  // Update verification details
  creator.verificationDetails = {
    ...creator.verificationDetails,
    reviewedAt: Date.now()
  };

  // Add rejection reason if status is rejected
  if (req.body.verificationStatus === 'rejected' && req.body.rejectionReason) {
    creator.verificationDetails.rejectionReason = req.body.rejectionReason;
  }

  // Save the updated creator
  await creator.save();

  // Send notification based on verification status
  try {
    const NotificationService = require('../../services/notification.service');

    if (req.body.verificationStatus === 'verified') {
      await NotificationService.createNotification({
        recipient: creator._id,
        type: 'creator_verified',
        title: 'Account Verified',
        message: 'Congratulations! Your creator account has been verified.',
        priority: 'high'
      });
    } else if (req.body.verificationStatus === 'rejected') {
      await NotificationService.createNotification({
        recipient: creator._id,
        type: 'creator_rejected',
        title: 'Verification Rejected',
        message: `Your creator account verification was rejected. Reason: ${creator.verificationDetails.rejectionReason || 'No reason provided'}`,
        priority: 'high'
      });
    }
  } catch (error) {
    console.error('Error sending creator verification notification:', error);
  }

  res.status(200).json({
    status: 'success',
    data: {
      creator
    }
  });
});

/**
 * Get creator's products
 * @route GET /api/v1/admin/creators/:id/products
 * @access Private (Admin only)
 */
exports.getCreatorProducts = catchAsync(async (req, res, next) => {
  // Check if creator exists
  const creator = await Creator.findById(req.params.id);

  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Build query
  const queryObj = { ...req.query, creator: req.params.id };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);

  let query = Product.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: searchRegex },
        { description: searchRegex }
      ]
    });
  }

  // Count total before applying pagination
  const total = await Product.countDocuments({ creator: req.params.id });

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const products = await query;

  res.status(200).json({
    status: 'success',
    results: products.length,
    total,
    page,
    limit,
    data: {
      products
    }
  });
});

/**
 * Get creator's bales
 * @route GET /api/v1/admin/creators/:id/bales
 * @access Private (Admin only)
 */
exports.getCreatorBales = catchAsync(async (req, res, next) => {
  // Check if creator exists
  const creator = await Creator.findById(req.params.id);

  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Build query
  const queryObj = { ...req.query, creator: req.params.id };
  const excludedFields = ['page', 'sort', 'limit', 'fields', 'search'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Advanced filtering
  let queryStr = JSON.stringify(queryObj);
  queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);

  let query = Bale.find(JSON.parse(queryStr));

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    query = query.find({
      $or: [
        { name: searchRegex },
        { description: searchRegex }
      ]
    });
  }

  // Count total before applying pagination
  const total = await Bale.countDocuments({ creator: req.params.id });

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const bales = await query;

  res.status(200).json({
    status: 'success',
    results: bales.length,
    total,
    page,
    limit,
    data: {
      bales
    }
  });
});

/**
 * Get creator's orders
 * @route GET /api/v1/admin/creators/:id/orders
 * @access Private (Admin only)
 */
exports.getCreatorOrders = catchAsync(async (req, res, next) => {
  // Check if creator exists
  const creator = await Creator.findById(req.params.id);

  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Build match stage with filters
  const matchStage = {
    'items.creator': new mongoose.Types.ObjectId(req.params.id)
  };

  // Filter by status if provided
  if (req.query.status) {
    matchStage.status = req.query.status;
  }

  // Filter by payment status if provided
  if (req.query.isPaid !== undefined) {
    matchStage.isPaid = req.query.isPaid === 'true';
  }

  // Filter by delivery status if provided
  if (req.query.isDelivered !== undefined) {
    matchStage.isDelivered = req.query.isDelivered === 'true';
  }

  // Filter by date range if provided
  if (req.query.startDate || req.query.endDate) {
    matchStage.createdAt = {};

    if (req.query.startDate) {
      matchStage.createdAt.$gte = new Date(req.query.startDate);
    }

    if (req.query.endDate) {
      const endDate = new Date(req.query.endDate);
      endDate.setHours(23, 59, 59, 999); // Set to end of day
      matchStage.createdAt.$lte = endDate;
    }
  }

  // Determine sort field and direction
  let sortStage = { createdAt: -1 }; // Default sort
  if (req.query.sort) {
    const sortFields = req.query.sort.split(',');
    sortStage = {};

    sortFields.forEach(field => {
      if (field.startsWith('-')) {
        sortStage[field.substring(1)] = -1;
      } else {
        sortStage[field] = 1;
      }
    });
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 20;
  const skip = (page - 1) * limit;

  // Count total before pagination for metadata
  const totalCount = await Order.countDocuments(matchStage);

  // Build the aggregation pipeline
  const pipeline = [
    {
      $match: matchStage
    },
    {
      $addFields: {
        creatorItems: {
          $filter: {
            input: '$items',
            as: 'item',
            cond: { $eq: ['$$item.creator', new mongoose.Types.ObjectId(req.params.id)] }
          }
        }
      }
    },
    {
      $project: {
        _id: 1,
        user: 1,
        creatorItems: 1,
        status: 1,
        total: 1,
        isPaid: 1,
        paidAt: 1,
        isDelivered: 1,
        deliveredAt: 1,
        createdAt: 1,
        updatedAt: 1,
        trackingNumber: 1,
        notes: 1,
        shippingAddress: 1,
        paymentMethod: 1,
        creatorTotal: {
          $sum: {
            $map: {
              input: '$creatorItems',
              as: 'item',
              in: { $multiply: ['$$item.price', '$$item.quantity'] }
            }
          }
        }
      }
    },
    {
      $sort: sortStage
    },
    {
      $skip: skip
    },
    {
      $limit: limit
    }
  ];

  // Add search functionality if search query is provided
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');

    // Add a $match stage for search after the initial $match but before $addFields
    pipeline.splice(1, 0, {
      $match: {
        $or: [
          { trackingNumber: searchRegex },
          { notes: searchRegex },
          { 'shippingAddress.name': searchRegex },
          { 'shippingAddress.city': searchRegex },
          { 'shippingAddress.state': searchRegex },
          { 'shippingAddress.country': searchRegex }
        ]
      }
    });
  }

  // Execute the aggregation pipeline
  const orders = await Order.aggregate(pipeline);

  // Populate user information
  await Order.populate(orders, {
    path: 'user',
    select: 'name email photo'
  });

  // Populate product information in creatorItems
  await Order.populate(orders, {
    path: 'creatorItems.product',
    select: 'name images'
  });

  res.status(200).json({
    status: 'success',
    results: orders.length,
    total: totalCount,
    page,
    limit,
    totalPages: Math.ceil(totalCount / limit),
    data: {
      orders
    }
  });
});

/**
 * Get creator verification statistics
 * @route GET /api/v1/admin/creators/verification-stats
 * @access Private (Admin only)
 */
exports.getVerificationStats = catchAsync(async (req, res, next) => {
  const stats = await Creator.aggregate([
    {
      $group: {
        _id: '$verificationStatus',
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);

  const totalCreators = await Creator.countDocuments();
  const unverifiedCreators = await Creator.countDocuments({ verificationStatus: 'unverified' });
  const pendingCreators = await Creator.countDocuments({ verificationStatus: 'pending' });
  const verifiedCreators = await Creator.countDocuments({ verificationStatus: 'verified' });
  const rejectedCreators = await Creator.countDocuments({ verificationStatus: 'rejected' });

  res.status(200).json({
    status: 'success',
    data: {
      stats,
      summary: {
        total: totalCreators,
        unverified: unverifiedCreators,
        pending: pendingCreators,
        verified: verifiedCreators,
        rejected: rejectedCreators
      }
    }
  });
});

/**
 * Get creator onboarding statistics
 * @route GET /api/v1/admin/creators/onboarding-stats
 * @access Private (Admin only)
 */
exports.getOnboardingStats = catchAsync(async (req, res, next) => {
  // Single aggregation query to get all stats
  const stats = await Creator.aggregate([
    {
      $group: {
        _id: '$onboardingStatus',
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);

  // Convert aggregation results to a more usable format
  const summary = {
    total: 0,
    pending: 0,
    completed: 0
  };

  // Fill in the summary with actual values
  stats.forEach(stat => {
    if (stat._id && summary.hasOwnProperty(stat._id)) {
      summary[stat._id] = stat.count;
      summary.total += stat.count;
    } else if (stat._id) {
      // Handle any new statuses not explicitly defined
      summary[stat._id] = stat.count;
      summary.total += stat.count;
    }
  });

  // Get additional onboarding progress stats
  const progressStats = await Creator.aggregate([
    {
      $group: {
        _id: null,
        totalCreators: { $sum: 1 },
        businessInfoCompleted: {
          $sum: { $cond: ["$onboardingProgress.businessInfo", 1, 0] }
        },
        paymentInfoCompleted: {
          $sum: { $cond: ["$onboardingProgress.paymentInfo", 1, 0] }
        },
        shopInfoCompleted: {
          $sum: { $cond: ["$onboardingProgress.shopInfo", 1, 0] }
        },
        shippingInfoCompleted: {
          $sum: { $cond: ["$onboardingProgress.shippingInfo", 1, 0] }
        }
      }
    }
  ]);

  // Extract progress data
  const progress = progressStats.length > 0 ? progressStats[0] : {
    totalCreators: 0,
    businessInfoCompleted: 0,
    paymentInfoCompleted: 0,
    shopInfoCompleted: 0,
    shippingInfoCompleted: 0
  };

  // Remove the _id field from progress
  delete progress._id;

  // Calculate percentages
  if (progress.totalCreators > 0) {
    progress.businessInfoCompletedPercentage = ((progress.businessInfoCompleted / progress.totalCreators) * 100).toFixed(2) + '%';
    progress.paymentInfoCompletedPercentage = ((progress.paymentInfoCompleted / progress.totalCreators) * 100).toFixed(2) + '%';
    progress.shopInfoCompletedPercentage = ((progress.shopInfoCompleted / progress.totalCreators) * 100).toFixed(2) + '%';
    progress.shippingInfoCompletedPercentage = ((progress.shippingInfoCompleted / progress.totalCreators) * 100).toFixed(2) + '%';
    progress.completedPercentage = ((summary.completed / progress.totalCreators) * 100).toFixed(2) + '%';
  }

  res.status(200).json({
    status: 'success',
    data: {
      totalCreators: summary.total,
      completionStatus: {
        complete: summary.completed || 0,
        notComplete: summary.pending || 0
      },
      onboardingProgress: progress,
      stats
    }
  });
});

/**
 * Get all reviews for products by a creator
 * @route GET /api/v1/admin/creators/:id/reviews
 * @access Private (Admin only)
 */
exports.getCreatorReviews = catchAsync(async (req, res, next) => {
  // Check if creator exists
  const creator = await Creator.findById(req.params.id);
  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Get all products by the creator
  const products = await Product.find({ creator: req.params.id }).select('_id');
  const productIds = products.map(product => product._id);

  // Get all bales by the creator
  const bales = await Bale.find({ creator: req.params.id }).select('_id');
  const baleIds = bales.map(bale => bale._id);

  // Get all reviews for the creator's products and bales
  const Review = require('../../models/review.model');

  // Build base query
  const baseQuery = {
    $or: [
      { product: { $in: productIds } },
      { bale: { $in: baleIds } },
      { creator: req.params.id }
    ]
  };

  // Add filters
  if (req.query.rating) {
    baseQuery.rating = parseInt(req.query.rating);
  }

  if (req.query.verified !== undefined) {
    baseQuery.verified = req.query.verified === 'true';
  }

  if (req.query.hidden !== undefined) {
    baseQuery.hidden = req.query.hidden === 'true';
  }

  // Date range filter
  if (req.query.startDate || req.query.endDate) {
    baseQuery.createdAt = {};

    if (req.query.startDate) {
      baseQuery.createdAt.$gte = new Date(req.query.startDate);
    }

    if (req.query.endDate) {
      const endDate = new Date(req.query.endDate);
      endDate.setHours(23, 59, 59, 999); // Set to end of day
      baseQuery.createdAt.$lte = endDate;
    }
  }

  // Search functionality
  if (req.query.search) {
    const searchRegex = new RegExp(req.query.search, 'i');
    baseQuery.$or = [
      { title: searchRegex },
      { review: searchRegex }
    ];
  }

  // Build query
  let query = Review.find(baseQuery);

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  }

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt');
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 10;
  const skip = (page - 1) * limit;

  query = query.skip(skip).limit(limit);

  // Execute query
  const reviews = await query
    .populate({
      path: 'user',
      select: 'name photo'
    })
    .populate({
      path: 'product',
      select: 'name images'
    })
    .populate({
      path: 'bale',
      select: 'name images'
    })
    .populate({
      path: 'creator',
      select: 'name photo'
    })
    .populate({
      path: 'order',
      select: '_id total'
    });

  // Get total count for pagination
  const totalReviews = await Review.countDocuments(baseQuery);

  res.status(200).json({
    status: 'success',
    results: reviews.length,
    totalReviews,
    totalPages: Math.ceil(totalReviews / limit),
    currentPage: page,
    data: {
      reviews
    }
  });
});

/**
 * Get earnings for a creator
 * @route GET /api/v1/admin/creators/:id/earnings
 * @access Private (Admin only)
 */
exports.getCreatorEarnings = catchAsync(async (req, res, next) => {
  // Check if creator exists
  const creator = await Creator.findById(req.params.id);
  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Get date range from query params or default to last 30 days
  const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
  const startDate = req.query.startDate ? new Date(req.query.startDate) : new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);

  // Set end date to end of day
  endDate.setHours(23, 59, 59, 999);

  // Set start date to beginning of day
  startDate.setHours(0, 0, 0, 0);

  // Get all orders with items from this creator within the date range
  const orders = await Order.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate, $lte: endDate },
        status: { $in: ['completed', 'delivered'] }
      }
    },
    {
      $unwind: '$items'
    },
    {
      $match: {
        'items.creator': new mongoose.Types.ObjectId(req.params.id)
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        },
        totalSales: { $sum: '$items.price' },
        totalItems: { $sum: '$items.quantity' },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
    }
  ]);

  // Calculate total earnings
  const totalEarnings = orders.reduce((acc, curr) => acc + curr.totalSales, 0);
  const totalItems = orders.reduce((acc, curr) => acc + curr.totalItems, 0);
  const totalOrders = orders.reduce((acc, curr) => acc + curr.count, 0);

  // Format the data for chart display
  const chartData = orders.map(item => ({
    date: `${item._id.year}-${item._id.month.toString().padStart(2, '0')}-${item._id.day.toString().padStart(2, '0')}`,
    sales: item.totalSales,
    items: item.totalItems,
    orders: item.count
  }));

  res.status(200).json({
    status: 'success',
    data: {
      totalEarnings,
      totalItems,
      totalOrders,
      chartData,
      dateRange: {
        startDate,
        endDate
      }
    }
  });
});

/**
 * Update business verification status for a creator
 * @route PATCH /api/v1/admin/creators/:id/business-verification
 * @access Private (Admin only)
 */
exports.updateBusinessVerification = catchAsync(async (req, res, next) => {
  // Check if creator exists
  const creator = await Creator.findById(req.params.id);
  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Check if required fields are provided
  if (!req.body.isVerified) {
    return next(new AppError('Please provide verification status', 400));
  }

  // Update business verification status
  const updatedCreator = await Creator.findByIdAndUpdate(
    req.params.id,
    {
      'businessInfo.isVerified': req.body.isVerified,
      'businessInfo.verificationDate': req.body.isVerified ? Date.now() : undefined,
      'businessInfo.verificationNotes': req.body.verificationNotes
    },
    {
      new: true,
      runValidators: true
    }
  );

  res.status(200).json({
    status: 'success',
    data: {
      creator: updatedCreator
    }
  });
});

/**
 * Delete creator
 * @route DELETE /api/v1/admin/creators/:id
 * @access Private (Admin only)
 */
exports.deleteCreator = catchAsync(async (req, res, next) => {
  // Find creator
  const creator = await Creator.findById(req.params.id);

  if (!creator) {
    return next(new AppError('No creator found with that ID', 404));
  }

  // Check if creator has products
  const productCount = await Product.countDocuments({ creator: req.params.id });
  const baleCount = await Bale.countDocuments({ creator: req.params.id });
  const orderCount = await Order.countDocuments({ 'items.creator': req.params.id });
  const reviewCount = await Review.countDocuments({ 'creator': req.params.id });

  // If creator has associated data, don't allow deletion
  if (productCount > 0 || baleCount > 0 || orderCount > 0 || reviewCount > 0) {
    return next(
      new AppError(
        `Cannot delete creator with existing data. Creator has ${productCount} products, ${baleCount} bales, ${orderCount} orders, and ${reviewCount} reviews.`,
        400
      )
    );
  }

  // Delete creator
  await Creator.findByIdAndDelete(req.params.id);

  res.status(204).json({
    status: 'success',
    data: null
  });
});