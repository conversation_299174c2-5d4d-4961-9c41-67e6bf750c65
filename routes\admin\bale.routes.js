const express = require('express');
const productController = require('../../controllers/admin/product.controller');
const authMiddleware = require('../../middleware/auth.middleware');

const router = express.Router();

// Protect all routes
router.use(authMiddleware.protect);
router.use(authMiddleware.restrictTo('admin'));


// Bale routes
router.get('/', productController.getAllBales);
router.get('/stats', productController.getBaleStats);
router.get('/:id', productController.getBale);
router.patch('/:id', productController.updateBale);
router.delete('/:id', productController.deleteBale);
router.patch('/:id/status', productController.updateBaleStatus);
router.get('/:id/reviews', productController.getBaleReviews);
router.get('/creators/:id', productController.getCreatorBales);

module.exports = router;


